package com.randomevents.events;

import net.minecraft.core.BlockPos;
import net.minecraft.server.MinecraftServer;
import net.minecraft.server.level.ServerLevel;
import net.minecraft.server.level.ServerPlayer;
import net.minecraft.world.entity.EntityType;
import net.minecraft.world.entity.animal.Chicken;
import net.minecraft.world.entity.item.ItemEntity;
import net.minecraft.world.item.ItemStack;
import net.minecraft.world.item.Items;

import java.util.ArrayList;
import java.util.List;
import java.util.Random;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

public class ChickenRainEvent extends RandomEvent {
    private static final ScheduledExecutorService scheduler = Executors.newSingleThreadScheduledExecutor();
    private static final Random random = new Random();
    
    @Override
    public String getId() {
        return "chicken_rain";
    }
    
    @Override
    public String getName() {
        return "§6§lChicken Rain!";
    }
    
    @Override
    public String getDescription() {
        return "MASSIVE chicken rain with diamond-pooping chickens for 2 minutes";
    }

    @Override
    public boolean canTrigger(MinecraftServer server) {
        // Prioritize this event for event 4, but allow it to trigger later if missed
        com.randomevents.manager.RandomEventManager manager = com.randomevents.RandomEventsMod.getEventManager();
        if (manager != null) {
            int currentEventNumber = manager.getCurrentEventNumber();
            // Trigger on event 4 or later (but prioritize event 4)
            return currentEventNumber >= 4;
        }
        return true; // Default to true if manager not available
    }
    
    @Override
    public void execute(MinecraftServer server) {
        
        // Show warning message
        for (ServerPlayer player : server.getPlayerList().getPlayers()) {
            player.connection.send(new net.minecraft.network.protocol.game.ClientboundSetActionBarTextPacket(
                net.minecraft.network.chat.Component.literal("§6§lBawk bawk! §7MASSIVE chicken invasion with diamond rewards!")));
        }
        
        // MASSIVE chicken rain for 2 minutes (every second)
        for (int i = 0; i < 120; i++) {
            scheduler.schedule(() -> {
                for (ServerPlayer player : server.getPlayerList().getPlayers()) {
                    ServerLevel level = player.serverLevel();
                    BlockPos playerPos = player.blockPosition();

                    // Drop 7-12 chickens around each player (reduced to prevent lag)
                    int chickenCount = 7 + random.nextInt(6);
                    for (int j = 0; j < chickenCount; j++) {
                        int x = playerPos.getX() + random.nextInt(50) - 25; // Larger area
                        int z = playerPos.getZ() + random.nextInt(50) - 25;
                        int y = level.getHeight(net.minecraft.world.level.levelgen.Heightmap.Types.MOTION_BLOCKING, x, z) + 25 + random.nextInt(20);

                        BlockPos spawnPos = new BlockPos(x, y, z);

                        // Make sure the spawn position is in bounds
                        if (level.isInWorldBounds(spawnPos)) {
                            // Thread-safe entity spawning
                            server.execute(() -> {
                                try {
                                    Chicken chicken = EntityType.CHICKEN.create(level);
                                    if (chicken != null) {
                                        chicken.moveTo(spawnPos.getX() + 0.5, spawnPos.getY(), spawnPos.getZ() + 0.5);

                                        // Make some chickens babies for variety
                                        if (random.nextInt(3) == 0) {
                                            chicken.setBaby(true);
                                        }

                                        // Give chickens some downward velocity to make them fall faster
                                        chicken.setDeltaMovement(0, -0.8, 0);

                                        level.addFreshEntity(chicken);
                                    }
                                } catch (Exception e) {
                                    // Prevent individual chicken spawn failures from crashing server
                                    System.err.println("Chicken spawn error: " + e.getMessage());
                                }
                            });
                        }
                    }
                }
            }, i * 1L, TimeUnit.SECONDS); // Every second instead of every 2 seconds
        }

        // Make chickens poop diamonds every 3 seconds for 2 minutes
        for (int i = 0; i < 40; i++) { // 40 times over 2 minutes (every 3 seconds)
            scheduler.schedule(() -> {
                // Thread-safe diamond dropping - find all chickens in the world
                for (ServerPlayer player : server.getPlayerList().getPlayers()) {
                    ServerLevel level = player.serverLevel();

                    // Find all chickens within 100 blocks of each player
                    List<Chicken> nearbyChickens = level.getEntitiesOfClass(Chicken.class,
                        new net.minecraft.world.phys.AABB(player.blockPosition()).inflate(100.0));

                    for (Chicken chicken : nearbyChickens) {
                        if (chicken.isAlive()) {
                            BlockPos chickenPos = chicken.blockPosition();

                            // 15% chance each chicken poops a diamond (reduced to prevent lag)
                            if (random.nextInt(100) < 15) {
                                // Thread-safe diamond spawning
                                server.execute(() -> {
                                    try {
                                        // Create diamond item
                                        ItemStack diamond = new ItemStack(Items.DIAMOND, 1);
                                        ItemEntity diamondEntity = new ItemEntity(level,
                                            chickenPos.getX() + 0.5, chickenPos.getY(), chickenPos.getZ() + 0.5, diamond);

                                        // Give the diamond a little upward velocity
                                        diamondEntity.setDeltaMovement(
                                            (random.nextDouble() - 0.5) * 0.2, // Random X
                                            0.2, // Upward Y
                                            (random.nextDouble() - 0.5) * 0.2  // Random Z
                                        );

                                        level.addFreshEntity(diamondEntity);
                                    } catch (Exception e) {
                                        // Prevent individual diamond spawn failures from crashing server
                                        System.err.println("Diamond spawn error: " + e.getMessage());
                                    }
                                });
                            }
                        }
                    }
                }
            }, i * 3L, TimeUnit.SECONDS); // Every 3 seconds
        }

        // Remove all chickens after 5 minutes (3 minutes after rain stops)
        scheduler.schedule(() -> {
            // Thread-safe chicken cleanup - find all chickens in the world
            for (ServerPlayer player : server.getPlayerList().getPlayers()) {
                ServerLevel level = player.serverLevel();

                // Find all chickens within 200 blocks of each player for cleanup
                List<Chicken> nearbyChickens = level.getEntitiesOfClass(Chicken.class,
                    new net.minecraft.world.phys.AABB(player.blockPosition()).inflate(200.0));

                for (Chicken chicken : nearbyChickens) {
                    if (chicken.isAlive()) {
                        server.execute(() -> {
                            try {
                                chicken.remove(net.minecraft.world.entity.Entity.RemovalReason.DISCARDED);
                            } catch (Exception e) {
                                // Prevent cleanup failures from crashing server
                                System.err.println("Chicken cleanup error: " + e.getMessage());
                            }
                        });
                    }
                }
            }
        }, 300L, TimeUnit.SECONDS);
    }
}
