package com.randomevents.events;

import net.minecraft.server.MinecraftServer;
import net.minecraft.server.level.ServerLevel;
import net.minecraft.server.level.ServerPlayer;
import net.minecraft.world.entity.item.ItemEntity;
import net.minecraft.world.item.ItemStack;
import net.minecraft.world.phys.Vec3;

import java.util.ArrayList;
import java.util.List;
import java.util.Random;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

public class DropHeldItemEvent extends RandomEvent {
    private static final Random random = new Random();
    private static final ScheduledExecutorService scheduler = Executors.newSingleThreadScheduledExecutor();
    
    @Override
    public String getId() {
        return "drop_held_item";
    }
    
    @Override
    public String getName() {
        return "§c§lDrop Held Item!";
    }
    
    @Override
    public String getDescription() {
        return "Your items don't like you anymore - up to 5 random items fly away from you";
    }
    
    @Override
    public void execute(MinecraftServer server) {
        for (ServerPlayer player : server.getPlayerList().getPlayers()) {
            // Show initial message for 5 seconds
            player.connection.send(new net.minecraft.network.protocol.game.ClientboundSetActionBarTextPacket(
                net.minecraft.network.chat.Component.literal("§c§lYour items don't like you anymore...")));

            // Start throwing items after 5 seconds
            scheduler.schedule(() -> {
                throwPlayerItems(player);
            }, 5L, TimeUnit.SECONDS);
        }
    }

    private void throwPlayerItems(ServerPlayer player) {
        ServerLevel level = player.serverLevel();
        List<ItemStack> itemsToThrow = new ArrayList<>();

        // Always include held item if they have one
        ItemStack heldItem = player.getMainHandItem();
        if (!heldItem.isEmpty()) {
            itemsToThrow.add(heldItem.copy());
            player.getMainHandItem().shrink(heldItem.getCount()); // Remove from hand
        }

        // Collect items from inventory and armor
        List<ItemStack> allItems = new ArrayList<>();

        // Add inventory items
        for (int i = 0; i < player.getInventory().getContainerSize(); i++) {
            ItemStack item = player.getInventory().getItem(i);
            if (!item.isEmpty()) {
                allItems.add(item);
            }
        }

        // Add armor items
        for (ItemStack armorItem : player.getInventory().armor) {
            if (!armorItem.isEmpty()) {
                allItems.add(armorItem);
            }
        }

        // Add offhand item
        ItemStack offhandItem = player.getOffhandItem();
        if (!offhandItem.isEmpty()) {
            allItems.add(offhandItem);
        }

        // Randomly select up to 4 more items (total of 5 including held item)
        int maxAdditionalItems = Math.min(4, allItems.size());
        for (int i = 0; i < maxAdditionalItems && itemsToThrow.size() < 5; i++) {
            if (!allItems.isEmpty()) {
                int randomIndex = random.nextInt(allItems.size());
                ItemStack selectedItem = allItems.get(randomIndex);
                itemsToThrow.add(selectedItem.copy());

                // Remove from player's inventory
                removeItemFromPlayer(player, selectedItem);
                allItems.remove(randomIndex);
            }
        }

        // Throw all selected items
        for (int i = 0; i < itemsToThrow.size(); i++) {
            final ItemStack itemToThrow = itemsToThrow.get(i);

            // Stagger the throwing with small delays
            scheduler.schedule(() -> {
                throwItem(player, level, itemToThrow);
            }, i * 500L, TimeUnit.MILLISECONDS); // 0.5 second intervals
        }
    }

    private void removeItemFromPlayer(ServerPlayer player, ItemStack itemToRemove) {
        // Remove from main inventory
        for (int i = 0; i < player.getInventory().getContainerSize(); i++) {
            ItemStack item = player.getInventory().getItem(i);
            if (ItemStack.isSameItemSameTags(item, itemToRemove)) {
                player.getInventory().setItem(i, ItemStack.EMPTY);
                return;
            }
        }

        // Remove from armor
        for (int i = 0; i < player.getInventory().armor.size(); i++) {
            ItemStack armorItem = player.getInventory().armor.get(i);
            if (ItemStack.isSameItemSameTags(armorItem, itemToRemove)) {
                player.getInventory().armor.set(i, ItemStack.EMPTY);
                return;
            }
        }

        // Remove from offhand
        if (ItemStack.isSameItemSameTags(player.getOffhandItem(), itemToRemove)) {
            player.setItemInHand(net.minecraft.world.InteractionHand.OFF_HAND, ItemStack.EMPTY);
        }
    }

    private void throwItem(ServerPlayer player, ServerLevel level, ItemStack item) {
        // Create item entity
        ItemEntity itemEntity = new ItemEntity(level, player.getX(), player.getY() + 1.0, player.getZ(), item);

        // Calculate random direction and distance (5-10 blocks away)
        double angle = random.nextDouble() * 2 * Math.PI; // Random angle
        double distance = 5 + random.nextDouble() * 5; // 5-10 blocks

        // MUCH higher velocity to ensure items land far away
        double velocityX = Math.cos(angle) * distance * 0.8; // Increased from 0.2 to 0.8
        double velocityZ = Math.sin(angle) * distance * 0.8; // Increased from 0.2 to 0.8
        double velocityY = 0.8 + random.nextDouble() * 0.6; // Higher upward arc (0.8-1.4)

        // Set the velocity to make it fly away
        itemEntity.setDeltaMovement(new Vec3(velocityX, velocityY, velocityZ));

        // Prevent immediate pickup - 3 second delay before player can pick it up
        itemEntity.setPickUpDelay(60); // 60 ticks = 3 seconds

        // Add to world
        level.addFreshEntity(itemEntity);

        // Show message
        player.connection.send(new net.minecraft.network.protocol.game.ClientboundSetActionBarTextPacket(
            net.minecraft.network.chat.Component.literal("§c§l" + item.getDisplayName().getString() + " §7flies away from you!")));
    }
}
