package com.randomevents.manager;

import com.randomevents.events.*;
import com.randomevents.network.EventCounterPacket;
import com.randomevents.network.NetworkHandler;
import com.randomevents.registry.EventRegistry;
import net.minecraft.nbt.CompoundTag;
import net.minecraft.nbt.ListTag;
import net.minecraft.nbt.StringTag;
import net.minecraft.network.chat.Component;
import net.minecraft.server.MinecraftServer;
import net.minecraft.server.level.ServerPlayer;
import net.minecraft.world.level.saveddata.SavedData;
import net.minecraft.world.level.storage.DimensionDataStorage;


import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ScheduledFuture;
import java.util.concurrent.TimeUnit;
import java.util.Map;
import java.util.HashMap;

public class RandomEventManager {
    private static final Logger LOGGER = LoggerFactory.getLogger(RandomEventManager.class);
    private static final String DATA_NAME = "RandomEventsData";
    
    private final MinecraftServer server;
    private final List<RandomEvent> allEvents;
    private final List<RandomEvent> availableEvents;
    private final Set<String> usedEventIds;
    private final Map<String, RandomEvent> eventLookup; // For O(1) event lookup
    private ScheduledExecutorService scheduler;

    private boolean gameActive = false;
    private boolean gamePaused = false;
    private ScheduledFuture<?> eventTask;
    private ScheduledFuture<?> counterTask;
    private EventData savedData;
    private int currentEventNumber = 0;
    private String currentEventName = "";

    // PAUSE/RESUME STATE MANAGEMENT
    private long pauseStartTime = 0;
    private long totalPausedTime = 0;
    private long eventStartTime = 0;
    private long remainingEventTime = 30000; // 30 seconds in milliseconds

    // Event tracking (removed intensive event restrictions)
    private final Set<String> activeEvents = new HashSet<>();
    private static final int MAX_CONCURRENT_EVENTS = 5; // Increased limit since events are shorter now

    // Thread safety for entity spawning
    private final Object entitySpawnLock = new Object();
    private volatile int currentEntitySpawnOperations = 0;
    private static final int MAX_CONCURRENT_ENTITY_OPERATIONS = 2;

    // OPTIMIZATION: Object pooling and caching
    private final ThreadLocal<StringBuilder> stringBuilderPool = ThreadLocal.withInitial(() -> new StringBuilder(256));
    private final Map<String, Long> eventDurationCache = new HashMap<>();
    private volatile long lastEventSelectionTime = 0;
    private static final long EVENT_SELECTION_CACHE_MS = 100; // Cache event selection for 100ms
    
    public RandomEventManager(MinecraftServer server) {
        this.server = server;
        this.allEvents = new ArrayList<>();
        this.availableEvents = new ArrayList<>();
        this.usedEventIds = new HashSet<>();
        this.eventLookup = new HashMap<>();
        // OPTIMIZED: Use cached thread pool for better performance
        this.scheduler = Executors.newScheduledThreadPool(2, r -> {
            Thread t = new Thread(r, "RandomEvents-Scheduler");
            t.setDaemon(true); // Don't prevent JVM shutdown
            t.setPriority(Thread.NORM_PRIORITY - 1); // Slightly lower priority
            return t;
        });

        initializeEvents();
        loadData();
    }
    
    private void initializeEvents() {
        // CRITICAL OPTIMIZATION: Use static event registry to avoid creating 69 objects every time
        // Events are stateless and can be safely reused
        allEvents.addAll(EventRegistry.getAllEvents());

        // Build lookup map for O(1) access
        for (RandomEvent event : allEvents) {
            eventLookup.put(event.getId(), event);
        }

        LOGGER.info("Initialized {} random events", allEvents.size());
    }
    
    private void loadData() {
        DimensionDataStorage storage = server.overworld().getDataStorage();
        savedData = storage.computeIfAbsent(EventData::load, EventData::new, DATA_NAME);
        
        usedEventIds.clear();
        usedEventIds.addAll(savedData.getUsedEvents());
        
        refreshAvailableEvents();
        LOGGER.info("Loaded event data. Used events: {}, Available events: {}", 
                   usedEventIds.size(), availableEvents.size());
    }
    
    private void refreshAvailableEvents() {
        availableEvents.clear();
        for (RandomEvent event : allEvents) {
            if (!usedEventIds.contains(event.getId())) {
                availableEvents.add(event);
            }
        }
        Collections.shuffle(availableEvents);
    }
    
    public boolean startEvents() {
        if (gameActive) {
            return false;
        }
        
        if (availableEvents.isEmpty()) {
            broadcastMessage("§cNo events available! Use /randomevents reset to reset the event pool.");
            return false;
        }
        
        gameActive = true;
        currentEventNumber = 0;
        currentEventName = "§7Waiting for first event...";
        startCounterDisplay();
        scheduleNextEvent();
        broadcastMessage("§a§lRandom Events Started!");
        LOGGER.info("Random events started. {} events available.", availableEvents.size());
        return true;
    }
    
    public boolean stopEvents() {
        if (!gameActive) {
            return false;
        }

        gameActive = false;
        gamePaused = false;
        if (eventTask != null) {
            eventTask.cancel(false);
            eventTask = null;
        }
        if (counterTask != null) {
            counterTask.cancel(false);
            counterTask = null;
        }

        // Clear active events tracking
        activeEvents.clear();

        // Clear the counter display
        EventCounterPacket hidePacket = new EventCounterPacket("", false);
        NetworkHandler.sendToAllPlayers(hidePacket);

        broadcastMessage("§c§lRandom Events Stopped!");
        LOGGER.info("Random events stopped.");
        return true;
    }

    public boolean pauseEvents() {
        if (!gameActive || gamePaused) {
            return false;
        }

        gamePaused = true;
        pauseStartTime = System.currentTimeMillis();

        // Calculate remaining time for current event cycle
        if (eventStartTime > 0) {
            long elapsedTime = pauseStartTime - eventStartTime;
            remainingEventTime = Math.max(1000, 30000 - elapsedTime); // At least 1 second remaining
            LOGGER.info("Paused with {} seconds remaining in current cycle", remainingEventTime / 1000.0);
        }

        // Cancel current event task but preserve counter
        if (eventTask != null) {
            eventTask.cancel(false);
            eventTask = null;
        }

        // Update counter to show paused state
        updateCounterDisplayPaused();

        broadcastMessage("§e§lRandom Events Paused! §7Event #" + currentEventNumber + " will resume...");
        LOGGER.info("Random events paused at event #{} with {:.1f}s remaining", currentEventNumber, remainingEventTime / 1000.0);
        return true;
    }

    public boolean resumeEvents() {
        if (!gameActive || !gamePaused) {
            return false;
        }

        gamePaused = false;

        // Calculate total paused time
        if (pauseStartTime > 0) {
            totalPausedTime += System.currentTimeMillis() - pauseStartTime;
            LOGGER.info("Resumed after {:.1f}s pause, {:.1f}s remaining in cycle",
                (System.currentTimeMillis() - pauseStartTime) / 1000.0, remainingEventTime / 1000.0);
        }

        // Reset event start time for accurate timing
        eventStartTime = System.currentTimeMillis();

        // Resume with remaining time (minimum 1 second)
        long resumeDelay = Math.max(1000, remainingEventTime);
        scheduleNextEventWithDelay(resumeDelay);

        // Resume normal counter display
        updateCounterDisplay();

        broadcastMessage("§a§lRandom Events Resumed! §7Next event in " + (resumeDelay / 1000) + " seconds...");
        LOGGER.info("Random events resumed at event #{}, next event in {:.1f}s", currentEventNumber, resumeDelay / 1000.0);
        return true;
    }
    
    public void resetEvents() {
        stopEvents();
        usedEventIds.clear();
        savedData.clearUsedEvents();
        savedData.setDirty();
        refreshAvailableEvents();

        // Reset counter
        currentEventNumber = 0;
        currentEventName = "";

        broadcastMessage("§e§lEvent pool reset! §rAll " + allEvents.size() + " events are now available.");
        LOGGER.info("Event pool reset. All {} events available.", allEvents.size());
    }
    
    private void scheduleNextEvent() {
        scheduleNextEventWithDelay(30000); // Default 30 seconds
    }

    private void scheduleNextEventWithDelay(long delayMillis) {
        if (!gameActive) return;

        // Cancel any existing event task to prevent overlapping timers
        if (eventTask != null && !eventTask.isDone()) {
            eventTask.cancel(false);
            LOGGER.debug("Cancelled existing event task to prevent timer overlap");
        }

        // Record when this event cycle starts
        eventStartTime = System.currentTimeMillis();
        remainingEventTime = delayMillis;

        eventTask = scheduler.schedule(() -> {
            if (gameActive && !gamePaused) {
                triggerRandomEvent();
                // Always schedule the next event, regardless of whether current event triggered
                scheduleNextEvent();
            } else if (gameActive && gamePaused) {
                // If paused, don't schedule anything - resume will handle it
                LOGGER.debug("Event cycle reached while paused, waiting for resume...");
            }
        }, delayMillis, TimeUnit.MILLISECONDS);

        LOGGER.debug("Scheduled next event in {:.1f} seconds. gameActive: {}, gamePaused: {}",
                    delayMillis / 1000.0, gameActive, gamePaused);
    }
    
    private void triggerRandomEvent() {
        if (availableEvents.isEmpty()) {
            broadcastMessage("§6§lAll events completed! §rUse /randomevents reset to play again.");
            stopEvents();
            return;
        }

        // Simplified event limiting - just check total concurrent events

        // OPTIMIZED: Special event prioritization with single pass
        prioritizeSpecialEvents();

        // OPTIMIZED: Find an event that can trigger under current conditions
        RandomEvent event = null;
        int attempts = 0;
        int maxAttempts = Math.min(availableEvents.size() * 2, 20); // Limit attempts to prevent infinite loops

        while (event == null && attempts < maxAttempts) {
            if (availableEvents.isEmpty()) {
                LOGGER.warn("Available events list became empty during selection");
                break;
            }

            RandomEvent candidateEvent = availableEvents.get(0);

            // Check if this event would exceed our limits (simplified - just check total events)
            boolean wouldExceedLimit = activeEvents.size() >= MAX_CONCURRENT_EVENTS;
            boolean canTrigger = candidateEvent.canTrigger(server);

            LOGGER.debug("Checking event: {} (attempt {}), canTrigger: {}, wouldExceedLimit: {}, currentEventNumber: {}, activeEvents: {}",
                candidateEvent.getId(), attempts + 1, canTrigger, wouldExceedLimit, currentEventNumber, activeEvents.size());

            if (canTrigger && !wouldExceedLimit) {
                event = candidateEvent;
                availableEvents.remove(0);
                LOGGER.info("Selected event: {} for event #{}", event.getId(), currentEventNumber + 1);
            } else {
                // Move this event to the back of the list and try the next one
                availableEvents.remove(0);
                availableEvents.add(candidateEvent);
                attempts++;
                LOGGER.debug("Skipped event: {} (canTrigger: {}, wouldExceedLimit: {})", candidateEvent.getId(), canTrigger, wouldExceedLimit);

                // OPTIMIZATION: If we've tried half the events and none can trigger,
                // check if we're in a stuck state
                if (attempts >= availableEvents.size() && attempts < maxAttempts) {
                    LOGGER.warn("Completed full cycle of events (attempt {}), none can trigger. Checking for stuck state...", attempts);

                    // Quick check: are ALL remaining events restricted by event number?
                    boolean allEventsRestricted = true;
                    for (RandomEvent checkEvent : availableEvents) {
                        if (checkEvent.canTrigger(server)) {
                            allEventsRestricted = false;
                            break;
                        }
                    }

                    if (allEventsRestricted) {
                        LOGGER.warn("All remaining events are restricted - system may be stuck at event #{}", currentEventNumber);
                        break; // Exit early to trigger fallback mechanism
                    }
                }
            }
        }

        // If no event can trigger, skip this cycle
        if (event == null) {
            if (activeEvents.size() >= MAX_CONCURRENT_EVENTS) {
                LOGGER.warn("Too many events running ({}), skipping cycle. Active events: {}", activeEvents.size(), activeEvents);
            } else {
                LOGGER.warn("No events can trigger under current conditions, skipping cycle. Available events: {}, attempts: {}", availableEvents.size(), attempts);
                LOGGER.warn("Current event number: {}, Active events: {}", currentEventNumber, activeEvents);

                // IMPROVED safety mechanism: Find ANY event that can trigger (respects restrictions)
                if (attempts >= availableEvents.size() && !availableEvents.isEmpty()) {
                    LOGGER.warn("No events can trigger normally, searching for ANY valid event...");
                    event = findAnyValidEvent();

                    if (event == null) {
                        LOGGER.warn("Emergency fallback: Clearing active events to unblock system");
                        // Emergency: Clear all active events and try again
                        activeEvents.clear();
                        event = findAnyValidEvent();

                        if (event != null) {
                            LOGGER.warn("Emergency: Cleared active events and found valid event: {}", event.getId());
                        } else {
                            LOGGER.error("CRITICAL: No events can trigger even after clearing active events! Available: {}, Current event #: {}",
                                availableEvents.size(), currentEventNumber);
                            return; // Skip this cycle completely
                        }
                    } else {
                        LOGGER.warn("Found valid event after full search: {}", event.getId());
                    }
                }
            }
        }

        usedEventIds.add(event.getId());
        savedData.addUsedEvent(event.getId());
        savedData.setDirty();

        // Add to active events tracking
        activeEvents.add(event.getId());

        // Update counter
        currentEventNumber++;
        currentEventName = event.getName();

        // Update counter display immediately when event triggers
        updateCounterDisplay();

        broadcastMessage("§c§l[RANDOM EVENT] §r" + event.getName());

        try {
            event.execute(server);
            LOGGER.info("Executed event: {} (ID: {})", event.getName(), event.getId());

            // Create final copy for lambda
            final String eventId = event.getId();

            // Schedule removal from active events after event duration
            scheduler.schedule(() -> {
                activeEvents.remove(eventId);
                LOGGER.debug("Event {} finished, removed from active events", eventId);
            }, getEventDuration(eventId), TimeUnit.SECONDS);

        } catch (Exception e) {
            LOGGER.error("Error executing event: {} (ID: {})", event.getName(), event.getId(), e);
            broadcastMessage("§cError executing event: " + event.getName());
            // Remove from active events if execution failed
            activeEvents.remove(event.getId());
        }
    }
    
    private void broadcastMessage(String message) {
        // OPTIMIZED: Create component once and reuse
        Component component = Component.literal(message);
        List<ServerPlayer> players = server.getPlayerList().getPlayers();

        // Batch send for better performance
        for (ServerPlayer player : players) {
            player.sendSystemMessage(component);
        }
    }
    
    public boolean isGameActive() {
        return gameActive;
    }
    
    public int getAvailableEventCount() {
        return availableEvents.size();
    }
    
    public int getTotalEventCount() {
        return allEvents.size();
    }
    
    public int getUsedEventCount() {
        return usedEventIds.size();
    }

    public int getCurrentEventNumber() {
        return currentEventNumber;
    }

    public String getCurrentEventName() {
        return currentEventName;
    }

    public void setCurrentEventNumber(int eventNumber) {
        this.currentEventNumber = eventNumber;

        // Update the counter display immediately
        updateCounterDisplay();

        // Save the new event number
        if (savedData != null) {
            savedData.setDirty();
        }

        LOGGER.info("Event number manually set to: {}", eventNumber);
    }

    public java.util.List<String> getUnusedEventNames() {
        java.util.List<String> unusedEvents = new java.util.ArrayList<>();
        for (RandomEvent event : availableEvents) {
            unusedEvents.add(event.getId());
        }
        return unusedEvents;
    }

    public boolean markEventAsUsed(String eventId) {
        // Check if event exists in the lookup
        if (!eventLookup.containsKey(eventId)) {
            LOGGER.warn("Attempted to mark non-existent event as used: {}", eventId);
            return false;
        }

        // Check if event is already used
        if (usedEventIds.contains(eventId)) {
            LOGGER.info("Event {} is already marked as used", eventId);
            return false;
        }

        // Mark event as used
        usedEventIds.add(eventId);
        savedData.addUsedEvent(eventId);
        savedData.setDirty();

        // Refresh available events to remove the used event
        refreshAvailableEvents();

        LOGGER.info("Manually marked event as used: {} (Available events: {})", eventId, availableEvents.size());
        return true;
    }

    public boolean setCurrentEventByName(String eventName) {
        // Find the event by name/ID
        RandomEvent targetEvent = eventLookup.get(eventName);
        if (targetEvent == null) {
            return false; // Event not found
        }

        // Set the current event name
        this.currentEventName = targetEvent.getName();

        // Update the counter display immediately
        updateCounterDisplay();

        // Save the change
        if (savedData != null) {
            savedData.setDirty();
        }

        LOGGER.info("Current event manually set to: {} ({})", targetEvent.getName(), eventName);
        return true;
    }

    private long getEventDuration(String eventId) {
        // Use optimized cached version
        return getEventDurationOptimized(eventId);
    }

    public boolean triggerSpecificEvent(String eventId) {
        // O(1) lookup using HashMap
        RandomEvent eventToTrigger = eventLookup.get(eventId);

        if (eventToTrigger == null) {
            return false; // Event not found
        }

        // Trigger the event WITHOUT chat message (silent execution)
        try {
            eventToTrigger.execute(server);
            LOGGER.info("Manually executed event: {} (ID: {})", eventToTrigger.getName(), eventToTrigger.getId());
            return true;
        } catch (Exception e) {
            LOGGER.error("Error manually executing event: {} (ID: {})", eventToTrigger.getName(), eventToTrigger.getId(), e);
            return false;
        }
    }

    public boolean triggerSpecificEventWithPlayerCount(String eventId, int playerCount) {
        // O(1) lookup using HashMap
        RandomEvent eventToTrigger = eventLookup.get(eventId);

        if (eventToTrigger == null) {
            return false; // Event not found
        }

        // Special handling for events that need player count simulation
        if (eventToTrigger instanceof com.randomevents.events.ArrowStormOceanEvent) {
            try {
                ((com.randomevents.events.ArrowStormOceanEvent) eventToTrigger).executeWithPlayerCount(server, playerCount);
                LOGGER.info("Manually executed event with player count: {} (ID: {}, players: {})", eventToTrigger.getName(), eventToTrigger.getId(), playerCount);
                return true;
            } catch (Exception e) {
                LOGGER.error("Error manually executing event with player count: {} (ID: {})", eventToTrigger.getName(), eventToTrigger.getId(), e);
                return false;
            }
        } else {
            // For other events, just execute normally
            return triggerSpecificEvent(eventId);
        }
    }

    /**
     * Thread-safe entity spawning utility for events
     * This helps prevent ConcurrentModificationException when multiple events spawn entities
     */
    public void safeEntitySpawn(Runnable entitySpawnTask) {
        synchronized (entitySpawnLock) {
            // Limit concurrent entity operations to prevent threading issues
            if (currentEntitySpawnOperations >= MAX_CONCURRENT_ENTITY_OPERATIONS) {
                LOGGER.warn("Too many concurrent entity operations, delaying spawn");
                // Schedule for later execution
                scheduler.schedule(() -> safeEntitySpawn(entitySpawnTask), 100L, TimeUnit.MILLISECONDS);
                return;
            }

            currentEntitySpawnOperations++;
            try {
                // Execute on main server thread to ensure thread safety
                server.execute(() -> {
                    try {
                        entitySpawnTask.run();
                    } catch (Exception e) {
                        LOGGER.error("Error during safe entity spawn: " + e.getMessage(), e);
                    } finally {
                        synchronized (entitySpawnLock) {
                            currentEntitySpawnOperations--;
                        }
                    }
                });
            } catch (Exception e) {
                synchronized (entitySpawnLock) {
                    currentEntitySpawnOperations--;
                }
                LOGGER.error("Error scheduling safe entity spawn: " + e.getMessage(), e);
            }
        }
    }

    public String debugClearActiveEvents() {
        int clearedCount = activeEvents.size();
        activeEvents.clear();

        // OPTIMIZED: Graceful scheduler restart with proper resource management
        ScheduledExecutorService oldScheduler = scheduler;
        scheduler = Executors.newScheduledThreadPool(2, r -> {
            Thread t = new Thread(r, "RandomEvents-Scheduler-New");
            t.setDaemon(true);
            t.setPriority(Thread.NORM_PRIORITY - 1);
            return t;
        });

        // Graceful shutdown with timeout
        oldScheduler.shutdown();
        try {
            if (!oldScheduler.awaitTermination(2, TimeUnit.SECONDS)) {
                oldScheduler.shutdownNow(); // Force if needed
            }
        } catch (InterruptedException e) {
            oldScheduler.shutdownNow();
            Thread.currentThread().interrupt();
        }

        LOGGER.info("Debug: Cleared {} active events and force-killed all background tasks", clearedCount);
        return "§e§lDEBUG: Cleared " + clearedCount + " active events and killed all background tasks.";
    }

    public String debugForceNextEvent() {
        if (!gameActive) {
            return "§c§lDEBUG: Events are not currently running!";
        }

        debugClearActiveEvents();

        // Cancel current scheduled event and trigger next one immediately
        if (eventTask != null) {
            eventTask.cancel(false);
            LOGGER.info("Debug: Cancelled existing event task");
        }

        // Trigger the next event immediately with SAFE selection (respects restrictions)
        eventTask = scheduler.schedule(() -> {
            if (gameActive) {
                LOGGER.info("Debug: Executing SAFE forced event trigger (respects event number restrictions)");
                triggerSafeRandomEvent(); // Use SAFE version that respects canTrigger()
                LOGGER.info("Debug: Scheduling next event to continue the loop");
                scheduleNextEvent(); // This ensures the event loop continues
            } else {
                LOGGER.warn("Debug: gameActive is false, not triggering event");
            }
        }, 2, TimeUnit.SECONDS); // Reduced to 2 seconds for faster debug

        LOGGER.info("Debug: Forced SAFE event scheduling with continued loop. gameActive: {}, availableEvents: {}", gameActive, availableEvents.size());
        return "§a§lDEBUG: Forcing next SAFE event in 2 seconds (respects restrictions)... Events will continue normally after.";
    }

    private void startCounterDisplay() {
        // OPTIMIZED: Update counter display every 3 seconds (reduced frequency)
        counterTask = scheduler.scheduleAtFixedRate(() -> {
            if (gameActive && !gamePaused) { // Don't update when paused
                updateCounterDisplay();
            }
        }, 0, 3, TimeUnit.SECONDS); // Reduced from 2 to 3 seconds
    }

    private void updateCounterDisplay() {
        String counterText;
        if (currentEventNumber == 0) {
            counterText = "Event Counter: Waiting for first event...";
        } else {
            // Clean text without color codes (we'll handle colors in the renderer)
            counterText = String.format("Event #%d - %s",
                currentEventNumber, stripColorCodes(currentEventName));
        }

        // Send custom packet to all players for client-side rendering
        EventCounterPacket packet = new EventCounterPacket(counterText, true);
        NetworkHandler.sendToAllPlayers(packet);
    }

    private void updateCounterDisplayPaused() {
        String counterText;
        if (currentEventNumber == 0) {
            counterText = "Event Counter: PAUSED - Waiting for first event...";
        } else {
            // Show paused state with remaining time
            long remainingSeconds = remainingEventTime / 1000;
            counterText = String.format("Event #%d - %s (PAUSED - %ds remaining)",
                currentEventNumber, stripColorCodes(currentEventName), remainingSeconds);
        }

        // Send custom packet to all players for client-side rendering
        EventCounterPacket packet = new EventCounterPacket(counterText, true);
        NetworkHandler.sendToAllPlayers(packet);
    }

    private String stripColorCodes(String text) {
        // OPTIMIZED: Use StringBuilder for efficient string manipulation
        if (text == null || text.isEmpty()) {
            return text;
        }

        StringBuilder sb = stringBuilderPool.get();
        sb.setLength(0); // Reset for reuse

        for (int i = 0; i < text.length(); i++) {
            char c = text.charAt(i);
            if (c == '§' && i + 1 < text.length()) {
                char next = text.charAt(i + 1);
                if ((next >= '0' && next <= '9') ||
                    (next >= 'a' && next <= 'f') ||
                    (next >= 'k' && next <= 'o') ||
                    next == 'r') {
                    i++; // Skip the color code
                    continue;
                }
            }
            sb.append(c);
        }

        return sb.toString();
    }

    public void shutdown() {
        stopEvents();
        scheduler.shutdown();
        try {
            if (!scheduler.awaitTermination(5, TimeUnit.SECONDS)) {
                scheduler.shutdownNow();
            }
        } catch (InterruptedException e) {
            scheduler.shutdownNow();
            Thread.currentThread().interrupt();
        }
    }
    
    // Saved data class for persistence
    public static class EventData extends SavedData {
        private final Set<String> usedEvents = new HashSet<>();
        
        public EventData() {}
        
        public static EventData load(CompoundTag tag) {
            EventData data = new EventData();
            ListTag usedList = tag.getList("UsedEvents", 8); // 8 = String tag type
            for (int i = 0; i < usedList.size(); i++) {
                data.usedEvents.add(usedList.getString(i));
            }
            return data;
        }
        
        @Override
        public CompoundTag save(CompoundTag tag) {
            ListTag usedList = new ListTag();
            for (String eventId : usedEvents) {
                usedList.add(StringTag.valueOf(eventId));
            }
            tag.put("UsedEvents", usedList);
            return tag;
        }
        
        public Set<String> getUsedEvents() {
            return new HashSet<>(usedEvents);
        }
        
        public void addUsedEvent(String eventId) {
            usedEvents.add(eventId);
        }
        
        public void clearUsedEvents() {
            usedEvents.clear();
        }
    }

    // OPTIMIZED METHODS FOR BETTER EVENT SELECTION

    /**
     * Safe version of triggerRandomEvent that respects all canTrigger() restrictions
     * Used by debug command to prevent triggering restricted events
     */
    private void triggerSafeRandomEvent() {
        if (availableEvents.isEmpty()) {
            LOGGER.warn("No available events to trigger safely");
            return;
        }

        // Find an event that can trigger under current conditions (respects all restrictions)
        RandomEvent event = findAnyValidEvent();

        if (event == null) {
            LOGGER.warn("SAFE TRIGGER: No events can trigger under current conditions, skipping cycle");
            return;
        }

        // Remove from available events
        availableEvents.remove(event);

        // Mark as used
        usedEventIds.add(event.getId());
        savedData.addUsedEvent(event.getId());
        savedData.setDirty();

        // Add to active events tracking
        activeEvents.add(event.getId());

        // Update counter
        currentEventNumber++;
        currentEventName = event.getName();

        // Update counter display immediately when event triggers
        updateCounterDisplay();

        broadcastMessage("§c§l[RANDOM EVENT] §r" + event.getName());

        try {
            event.execute(server);
            LOGGER.info("SAFE TRIGGER: Executed event: {} (ID: {})", event.getName(), event.getId());

            // Create final copy for lambda
            final String eventId = event.getId();

            // Schedule removal from active events after event duration
            scheduler.schedule(() -> {
                activeEvents.remove(eventId);
                LOGGER.debug("Event {} finished, removed from active events", eventId);
            }, getEventDuration(eventId), TimeUnit.SECONDS);

        } catch (Exception e) {
            LOGGER.error("Error executing SAFE event: {} (ID: {})", event.getName(), event.getId(), e);
            broadcastMessage("§cError executing event: " + event.getName());
            // Remove from active events if execution failed
            activeEvents.remove(event.getId());
        }
    }

    /**
     * Optimized method to find ANY valid event that can trigger
     * Respects all canTrigger() restrictions including event number limits
     */
    private RandomEvent findAnyValidEvent() {
        // First pass: Try events in current order (most efficient)
        for (RandomEvent candidateEvent : availableEvents) {
            boolean wouldExceedLimit = activeEvents.size() >= MAX_CONCURRENT_EVENTS;
            boolean canTrigger = candidateEvent.canTrigger(server);

            if (canTrigger && !wouldExceedLimit) {
                LOGGER.debug("Found valid event in first pass: {}", candidateEvent.getId());
                return candidateEvent;
            }
        }

        // Second pass: If concurrent limit is the issue, find events that can trigger regardless
        if (activeEvents.size() >= MAX_CONCURRENT_EVENTS) {
            LOGGER.debug("Concurrent limit reached, looking for any event that can trigger");
            for (RandomEvent candidateEvent : availableEvents) {
                if (candidateEvent.canTrigger(server)) {
                    LOGGER.debug("Found valid event ignoring concurrent limit: {}", candidateEvent.getId());
                    return candidateEvent;
                }
            }
        }

        LOGGER.warn("No valid events found. Available: {}, Active: {}, Current event #: {}",
            availableEvents.size(), activeEvents.size(), currentEventNumber);

        // Debug: Log which events are available and why they can't trigger
        for (RandomEvent candidateEvent : availableEvents) {
            boolean canTrigger = candidateEvent.canTrigger(server);
            LOGGER.debug("Event {} canTrigger: {}", candidateEvent.getId(), canTrigger);
        }

        return null;
    }

    // OPTIMIZATION METHODS

    /**
     * Optimized special event prioritization with single pass
     */
    private void prioritizeSpecialEvents() {
        int nextEventNumber = currentEventNumber + 1;
        String targetEventId = null;

        // Determine target event based on number
        switch (nextEventNumber) {
            case 4:
                targetEventId = "chicken_rain";
                break;
            case 60:
                targetEventId = "chaos_theory";
                break;
            case 61:
                targetEventId = "nether_invasion";
                break;
            default:
                return; // No special prioritization needed
        }

        // Single pass to find and prioritize target event
        for (int i = 0; i < availableEvents.size(); i++) {
            RandomEvent candidateEvent = availableEvents.get(i);
            if (targetEventId.equals(candidateEvent.getId())) {
                // Move to front efficiently
                if (i > 0) {
                    availableEvents.remove(i);
                    availableEvents.add(0, candidateEvent);
                    LOGGER.info("Prioritized {} for event #{}", targetEventId, nextEventNumber);
                }
                break;
            }
        }
    }

    /**
     * Optimized event duration lookup with caching
     */
    private long getEventDurationOptimized(String eventId) {
        // Check cache first
        Long cachedDuration = eventDurationCache.get(eventId);
        if (cachedDuration != null) {
            return cachedDuration;
        }

        // Calculate and cache duration
        long duration = calculateEventDuration(eventId);
        eventDurationCache.put(eventId, duration);
        return duration;
    }

    /**
     * Calculate event duration (extracted from getEventDuration for optimization)
     */
    private long calculateEventDuration(String eventId) {
        // Use switch with string hash for better performance than if-else chain
        switch (eventId) {
            case "anvil_rain":
            case "lava_rain":
            case "item_rain":
            case "chicken_rain":
                return 45L;
            case "tsunami_wave":
            case "everything_explodes":
            case "demolition_day":
            case "tree_explosion":
            case "lava_geyser":
            case "ice_age":
            case "half_heart_challenge":
            case "all_tools_are_pickaxes":
                return 30L;
            case "heavy_snow":
                return 35L;
            case "desert_storm":
            case "tornado":
                return 60L;
            case "sinking_world":
            case "lava_ocean":
            case "underground_ocean":
            case "trust_fall":
                return 120L;
            case "arrow_storm_ocean":
            case "mob_launch":
            case "enderman_invasion":
                return 90L;
            case "nether_invasion":
                return 180L;
            case "chaos_theory":
            case "ultimate_event":
                return 150L;
            case "creeper_rodeo":
                return 15L;
            case "climb_the_ladder":
                return 300L;
            case "item_bomb":
                return 5L;
            default:
                return 60L;
        }
    }

    /**
     * OPTIMIZATION: Cleanup method for proper resource management
     * Call this when the mod is being unloaded or server is shutting down
     */
    public void cleanup() {
        try {
            // Stop all events
            stopEvents();

            // Clear all caches
            eventDurationCache.clear();
            stringBuilderPool.remove();

            // Shutdown scheduler gracefully
            if (scheduler != null && !scheduler.isShutdown()) {
                scheduler.shutdown();
                try {
                    if (!scheduler.awaitTermination(5, TimeUnit.SECONDS)) {
                        scheduler.shutdownNow();
                    }
                } catch (InterruptedException e) {
                    scheduler.shutdownNow();
                    Thread.currentThread().interrupt();
                }
            }

            LOGGER.info("RandomEventManager cleanup completed");
        } catch (Exception e) {
            LOGGER.error("Error during RandomEventManager cleanup", e);
        }
    }
}

