package com.randomevents.events;

import net.minecraft.core.BlockPos;
import net.minecraft.server.MinecraftServer;
import net.minecraft.server.level.ServerLevel;
import net.minecraft.server.level.ServerPlayer;
import net.minecraft.world.level.block.Blocks;
import net.minecraft.world.phys.Vec3;

import net.minecraftforge.common.MinecraftForge;
import net.minecraftforge.event.entity.living.LivingEvent;
import net.minecraftforge.eventbus.api.SubscribeEvent;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ScheduledFuture;
import java.util.concurrent.TimeUnit;

public class TsunamiWaveEvent extends RandomEvent {
    // OPTIMIZED: Use daemon thread with proper naming and priority
    private static final ScheduledExecutorService scheduler = Executors.newSingleThreadScheduledExecutor(r -> {
        Thread t = new Thread(r, "TsunamiWave-Scheduler");
        t.setDaemon(true);
        t.setPriority(Thread.NORM_PRIORITY - 1);
        return t;
    });

    // OPTIMIZED: Use concurrent collections for thread safety
    private final Set<BlockPos> placedWaterBlocks = ConcurrentHashMap.newKeySet();
    private static volatile boolean tsunamiActive = false;
    private final List<ScheduledFuture<?>> scheduledTasks = Collections.synchronizedList(new ArrayList<>());
    private static TsunamiEventHandler eventHandler;
    
    @Override
    public String getId() {
        return "tsunami_wave";
    }
    
    @Override
    public String getName() {
        return "§1§lTsunami Wave!";
    }
    
    @Override
    public String getDescription() {
        return "Realistic tsunami rises then crashes with massive force, pushing players";
    }
    
    @Override
    public void execute(MinecraftServer server) {
        placedWaterBlocks.clear();
        scheduledTasks.clear();
        tsunamiActive = true;

        // Register event listener for water pushing
        eventHandler = new TsunamiEventHandler();
        MinecraftForge.EVENT_BUS.register(eventHandler);

        // Physics effects for exactly 30 seconds - NO TEXT
        for (int i = 0; i < 60; i++) { // 30 seconds * 2 checks per second = 60 checks
            ScheduledFuture<?> task = scheduler.schedule(() -> {
                if (tsunamiActive) {
                    for (ServerPlayer player : server.getPlayerList().getPlayers()) {
                        ServerLevel level = player.serverLevel();
                        BlockPos playerPos = player.blockPosition();

                        // Check if player is in water (any water contact)
                        boolean inWater = level.getBlockState(playerPos).is(Blocks.WATER) ||
                                         level.getBlockState(playerPos.above()).is(Blocks.WATER) ||
                                         level.getBlockState(playerPos.below()).is(Blocks.WATER) ||
                                         level.getBlockState(playerPos.north()).is(Blocks.WATER) ||
                                         level.getBlockState(playerPos.south()).is(Blocks.WATER) ||
                                         level.getBlockState(playerPos.east()).is(Blocks.WATER) ||
                                         level.getBlockState(playerPos.west()).is(Blocks.WATER);

                        if (inWater) {
                            // TSUNAMI SPIRALING PHYSICS - UP/DOWN SPIRALING + FORWARD/BACKWARD PUSHING

                            // 1. VERTICAL SPIRALING motion - up and down in the water
                            double time = System.currentTimeMillis() * 0.03; // Fast time progression
                            double verticalSpiral = Math.sin(time) * 6.0; // Strong up/down spiraling

                            // 2. FORWARD/BACKWARD PUSHING forces
                            double pushTime = time * 0.5; // Slower push rhythm
                            double forwardPush = Math.cos(pushTime) * 8.0; // Strong forward/backward push

                            // 3. HORIZONTAL SPIRALING (left/right)
                            double horizontalSpiral = Math.cos(time * 1.5) * 4.0; // Side-to-side spiraling

                            // 4. Determine push direction based on player facing
                            Vec3 lookDirection = player.getLookAngle();
                            double pushX = lookDirection.x * forwardPush + horizontalSpiral;
                            double pushZ = lookDirection.z * forwardPush;

                            // 5. APPLY TSUNAMI FORCES - vertical spiraling + directional pushing
                            player.setDeltaMovement(pushX, verticalSpiral, pushZ);

                            // 6. MODERATE view spinning for disorientation (not extreme)
                            player.setYRot(player.getYRot() + 30); // Moderate rotation
                            player.setXRot(player.getXRot() + (float)(Math.random() - 0.5) * 60); // Moderate pitch changes

                            // NO TEXT MESSAGES - removed all screen text
                        }
                    }
                }
            }, i * 500L, TimeUnit.MILLISECONDS); // Every 500ms (0.5 seconds)
            scheduledTasks.add(task);
        }

        // Phase 1: Create wave walls - LIMITED TO 20 SECONDS MAX
        for (int i = 0; i < 20; i++) { // Only 20 waves over 20 seconds = every 1 second
            final int waveLevel = i;
            ScheduledFuture<?> waveTask = scheduler.schedule(() -> {
                if (!tsunamiActive) return; // Stop if tsunami ended
                for (ServerPlayer player : server.getPlayerList().getPlayers()) {
                    ServerLevel level = player.serverLevel();
                    BlockPos playerPos = player.blockPosition();

                    // Create tsunami wave that starts FAR and moves toward player
                    int waveDistance = 40 - (waveLevel * 2); // Wave starts 40 blocks away and moves 2 blocks closer every second
                    int waveHeight = 20; // Reduced height to 20 blocks

                    // Create the approaching wave wall from all 4 directions
                    for (int direction = 0; direction < 4; direction++) {
                        int startX = playerPos.getX();
                        int startZ = playerPos.getZ();

                        // Calculate wave position based on direction
                        switch (direction) {
                            case 0: startX -= waveDistance; break; // From west
                            case 1: startX += waveDistance; break; // From east
                            case 2: startZ -= waveDistance; break; // From north
                            case 3: startZ += waveDistance; break; // From south
                        }

                        // Create wave wall - reduced size
                        for (int offset = -40; offset <= 40; offset++) { // Smaller wave width
                            for (int height = 0; height <= waveHeight; height++) {
                                BlockPos wavePos;
                                if (direction < 2) { // East/West waves
                                    wavePos = new BlockPos(startX, playerPos.getY() + height, playerPos.getZ() + offset);
                                } else { // North/South waves
                                    wavePos = new BlockPos(playerPos.getX() + offset, playerPos.getY() + height, startZ);
                                }

                                if (level.isInWorldBounds(wavePos) && level.getBlockState(wavePos).isAir()) {
                                    // Thread-safe block placement
                                    server.execute(() -> {
                                        try {
                                            // Use flag 2 to prevent block updates and reduce tick load
                                            level.setBlock(wavePos, Blocks.WATER.defaultBlockState(), 2);
                                            placedWaterBlocks.add(wavePos);
                                        } catch (Exception e) {
                                            // Prevent individual block placement failures from crashing server
                                            System.err.println("Tsunami water block placement error: " + e.getMessage());
                                        }
                                    });
                                }
                            }
                        }
                    }
                }
                // NO TEXT MESSAGES - removed all screen text
            }, i * 1000L, TimeUnit.MILLISECONDS); // Every 1 second instead of 0.5 seconds
            scheduledTasks.add(waveTask);
        }

        // Phase 2: Final wave crash - LIMITED TO 8 SECONDS MAX (20s to 28s)
        ScheduledFuture<?> phase2Task = scheduler.schedule(() -> {
            if (!tsunamiActive) return; // Stop if tsunami ended

            // Create the final wave that moves across the map - REDUCED SIZE AND COUNT
            for (int wave = 0; wave < 6; wave++) { // Only 6 waves (ends at 26s, well before 30s limit)
                final int waveNumber = wave;

                ScheduledFuture<?> massiveWaveTask = scheduler.schedule(() -> {
                    if (!tsunamiActive) return; // Stop if tsunami ended
                    for (ServerPlayer player : server.getPlayerList().getPlayers()) {
                        ServerLevel level = player.serverLevel();
                        BlockPos playerPos = player.blockPosition();

                        // Create a moving wall of water - REDUCED SIZE
                        int waveOffset = waveNumber * 6; // Wave moves 6 blocks per second (slower)

                        for (int x = -30; x <= 30; x++) { // Smaller wave width
                            for (int z = -10 + waveOffset; z <= 10 + waveOffset; z++) { // Thinner wave
                                for (int y = 0; y <= 15; y++) { // 15 blocks high (reduced)
                                    BlockPos wavePos = playerPos.offset(x, y, z);

                                    if (level.isInWorldBounds(wavePos) &&
                                        level.getBlockState(wavePos).isAir()) {
                                        // Thread-safe block placement
                                        server.execute(() -> {
                                            try {
                                                // Use flag 2 to prevent block updates and reduce tick load
                                                level.setBlock(wavePos, Blocks.WATER.defaultBlockState(), 2);
                                                placedWaterBlocks.add(wavePos);
                                            } catch (Exception e) {
                                                // Prevent individual block placement failures from crashing server
                                                System.err.println("Tsunami wave block placement error: " + e.getMessage());
                                            }
                                        });
                                    }
                                }
                            }
                        }
                    }
                }, wave * 1L, TimeUnit.SECONDS);
                scheduledTasks.add(massiveWaveTask);
            }
        }, 20L, TimeUnit.SECONDS); // Start at 20 seconds
        scheduledTasks.add(phase2Task);

        // CRITICAL: Stop tsunami effect after EXACTLY 30 seconds
        ScheduledFuture<?> stopTask = scheduler.schedule(() -> {
            tsunamiActive = false;

            // Unregister event handler
            try {
                if (eventHandler != null) {
                    MinecraftForge.EVENT_BUS.unregister(eventHandler);
                    eventHandler = null;
                }
            } catch (Exception e) {
                // Ignore unregister errors
            }

            // FORCE CANCEL all remaining scheduled tasks immediately
            for (ScheduledFuture<?> task : scheduledTasks) {
                if (!task.isDone()) {
                    task.cancel(true); // Use true to interrupt if running
                }
            }
            scheduledTasks.clear();

            // FIXED: Clear tracking data immediately at 30 seconds (not 31!)
            placedWaterBlocks.clear();

            // NO TEXT MESSAGES - removed all screen text
        }, 30L, TimeUnit.SECONDS);
        scheduledTasks.add(stopTask);

        // REMOVED: No additional tasks after 30 seconds - everything happens at exactly 30s
    }

    public static class TsunamiEventHandler {
        @SubscribeEvent
        public void onLivingUpdate(LivingEvent.LivingTickEvent event) {
            if (!tsunamiActive || !(event.getEntity() instanceof ServerPlayer player)) return;

            ServerLevel level = player.serverLevel();
            BlockPos playerPos = player.blockPosition();

            // Check if player is in water (EXPANDED detection)
            boolean inWater = level.getBlockState(playerPos).is(Blocks.WATER) ||
                             level.getBlockState(playerPos.above()).is(Blocks.WATER) ||
                             level.getBlockState(playerPos.below()).is(Blocks.WATER) ||
                             level.getBlockState(playerPos.north()).is(Blocks.WATER) ||
                             level.getBlockState(playerPos.south()).is(Blocks.WATER) ||
                             level.getBlockState(playerPos.east()).is(Blocks.WATER) ||
                             level.getBlockState(playerPos.west()).is(Blocks.WATER) ||
                             player.isInWater(); // Also check Minecraft's built-in water detection

            if (inWater) {
                // TSUNAMI SPIRALING PHYSICS - VERTICAL SPIRALING + DIRECTIONAL PUSHING

                // 1. STRONG VERTICAL SPIRALING - up and down motion
                double spiralTime = System.currentTimeMillis() * 0.04; // Fast spiraling
                double verticalSpiral = Math.sin(spiralTime) * 8.0; // Strong up/down motion

                // 2. DIRECTIONAL PUSHING - forward and backward forces
                double pushTime = spiralTime * 0.6; // Different rhythm for pushing
                Vec3 lookDirection = player.getLookAngle();
                double forwardPush = Math.cos(pushTime) * 10.0; // Strong directional push

                // 3. HORIZONTAL SPIRALING - side to side motion
                double horizontalSpiral = Math.cos(spiralTime * 1.8) * 5.0; // Side spiraling

                // 4. CALCULATE FINAL FORCES
                double pushX = lookDirection.x * forwardPush + horizontalSpiral;
                double pushZ = lookDirection.z * forwardPush;
                double pushY = verticalSpiral;

                // 5. APPLY TSUNAMI FORCES
                player.setDeltaMovement(pushX, pushY, pushZ);

                // 6. MODERATE view rotation for realism
                player.setYRot(player.getYRot() + 45); // Moderate rotation
                player.setXRot(player.getXRot() + (float)(Math.random() - 0.5) * 90); // Moderate pitch

                // NO TEXT MESSAGES - removed all screen text
            }
        }
    }
}
