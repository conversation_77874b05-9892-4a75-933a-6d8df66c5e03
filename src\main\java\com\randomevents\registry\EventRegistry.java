package com.randomevents.registry;

import com.randomevents.events.*;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * CRITICAL OPTIMIZATION: Static event registry to avoid creating 69 event objects repeatedly
 * Events are stateless and can be safely reused across multiple manager instances
 */
public class EventRegistry {
    
    // Singleton instances - created once and reused
    private static final List<RandomEvent> ALL_EVENTS = new ArrayList<>();
    
    static {
        // Initialize all events once at class loading time
        initializeEventRegistry();
    }
    
    private static void initializeEventRegistry() {
        // Add all 69 events (stateless singletons)
        ALL_EVENTS.add(new InstantHungerEvent());
        ALL_EVENTS.add(new HealthSwapEvent());
        ALL_EVENTS.add(new HalfHeartChallengeEvent());
        ALL_EVENTS.add(new BlindnessBlastEvent());
        ALL_EVENTS.add(new SlowMotionEvent());
        ALL_EVENTS.add(new MiningFatigueEvent());
        ALL_EVENTS.add(new LevitationLiftEvent());
        ALL_EVENTS.add(new LightningStormEvent());
        ALL_EVENTS.add(new InstantNightEvent());
        ALL_EVENTS.add(new WeatherChaosEvent());
        ALL_EVENTS.add(new LavaRainEvent());
        ALL_EVENTS.add(new IceAgeEvent());
        ALL_EVENTS.add(new DesertStormEvent());
        ALL_EVENTS.add(new TreeExplosionEvent());
        ALL_EVENTS.add(new SkyIslandsEvent());
        ALL_EVENTS.add(new CowChaosEvent());
        ALL_EVENTS.add(new InventoryBombEvent());
        ALL_EVENTS.add(new RandomTeleportEvent());
        ALL_EVENTS.add(new PlayerSwapEvent());
        ALL_EVENTS.add(new LeafBombEvent());
        ALL_EVENTS.add(new BedrockHoleEvent());
        ALL_EVENTS.add(new VillagerTakeoverEvent());
        // Batch 1 events
        ALL_EVENTS.add(new BackwardsControlsEvent());
        ALL_EVENTS.add(new BabyZombieSwarmEvent());
        ALL_EVENTS.add(new HotPotatoBombEvent());
        ALL_EVENTS.add(new ItemRainEvent());
        ALL_EVENTS.add(new SuperSpeedEvent());
        ALL_EVENTS.add(new ChickenRainEvent());
        ALL_EVENTS.add(new DancePartyEvent());
        ALL_EVENTS.add(new ChaosTheoryEvent());
        // Batch 2 events
        ALL_EVENTS.add(new TsunamiWaveEvent());
        ALL_EVENTS.add(new TornadoEvent());
        ALL_EVENTS.add(new XRayVisionEvent());
        ALL_EVENTS.add(new EverythingExplodesEvent());
        ALL_EVENTS.add(new SpotlightEvent());
        ALL_EVENTS.add(new DemolitionDayEvent());
        // New events
        ALL_EVENTS.add(new CrouchTNTEvent());
        ALL_EVENTS.add(new ArrowStormOceanEvent());
        ALL_EVENTS.add(new AnvilRainEvent());
        ALL_EVENTS.add(new SinkingWorldEvent());
        ALL_EVENTS.add(new MobLaunchEvent());
        ALL_EVENTS.add(new ColorMatchPuzzleEvent());
        ALL_EVENTS.add(new LavaGeyserEvent());
        ALL_EVENTS.add(new DiamondDowngradeEvent());
        ALL_EVENTS.add(new HumanChainEvent());
        ALL_EVENTS.add(new HeavySnowEvent());
        ALL_EVENTS.add(new MobMagnetsEvent());
        ALL_EVENTS.add(new DiamondParadiseEvent());
        ALL_EVENTS.add(new MagmaAgeEvent());
        ALL_EVENTS.add(new CreeperRodeoEvent());
        ALL_EVENTS.add(new ClimbTheLadderEvent());
        ALL_EVENTS.add(new SlotMachineInventoryEvent());
        ALL_EVENTS.add(new DropHeldItemEvent());
        ALL_EVENTS.add(new AllToolsArePickaxesEvent());
        ALL_EVENTS.add(new TrustFallEvent());
        ALL_EVENTS.add(new VillagerJudgeEvent());
        ALL_EVENTS.add(new CraftAwayEvent());
        ALL_EVENTS.add(new NetherInvasionEvent());
        ALL_EVENTS.add(new UltimateEvent());
        ALL_EVENTS.add(new ExecutionCeremonyEvent());
        ALL_EVENTS.add(new ItemBombEvent());
        ALL_EVENTS.add(new GiantFoodEvent());
        
        // Make the list immutable for thread safety
        // Note: We don't use Collections.unmodifiableList() here because
        // RandomEventManager needs to modify its own copy
    }
    
    /**
     * Get all available events (returns a new list to prevent external modification)
     * @return List of all events
     */
    public static List<RandomEvent> getAllEvents() {
        return new ArrayList<>(ALL_EVENTS);
    }
    
    /**
     * Get the total number of events
     * @return Event count
     */
    public static int getEventCount() {
        return ALL_EVENTS.size();
    }
    
    /**
     * Get event by ID (O(n) lookup - use RandomEventManager's lookup map for O(1))
     * @param eventId The event ID
     * @return The event or null if not found
     */
    public static RandomEvent getEventById(String eventId) {
        for (RandomEvent event : ALL_EVENTS) {
            if (event.getId().equals(eventId)) {
                return event;
            }
        }
        return null;
    }
}
