package com.randomevents.events;

import net.minecraft.core.BlockPos;
import net.minecraft.server.MinecraftServer;
import net.minecraft.server.level.ServerLevel;
import net.minecraft.server.level.ServerPlayer;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.sounds.SoundSource;
import net.minecraft.world.level.block.state.BlockState;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

public class PlayerSwapEvent extends RandomEvent {
    
    @Override
    public String getId() {
        return "player_swap";
    }
    
    @Override
    public String getName() {
        return "§3§lPlayer Swap!";
    }
    
    @Override
    public String getDescription() {
        return "All players swap positions with each other";
    }
    
    @Override
    public void execute(MinecraftServer server) {
        List<ServerPlayer> players = new ArrayList<>(server.getPlayerList().getPlayers());
        if (players.size() < 2) return;

        // Store complete player location data (position + dimension together)
        List<PlayerLocation> playerLocations = new ArrayList<>();

        for (ServerPlayer player : players) {
            playerLocations.add(new PlayerLocation(
                player.blockPosition(),
                player.serverLevel(),
                player.getYRot(),
                player.getXRot()
            ));
        }

        // Shuffle the complete locations (keeps position and dimension together)
        Collections.shuffle(playerLocations);

        // Show swap message
        for (ServerPlayer player : players) {
            player.connection.send(new net.minecraft.network.protocol.game.ClientboundSetActionBarTextPacket(
                net.minecraft.network.chat.Component.literal("§3§lPositions swapped!")));
        }

        // Teleport players to shuffled locations
        for (int i = 0; i < players.size(); i++) {
            ServerPlayer player = players.get(i);
            PlayerLocation newLocation = playerLocations.get(i);

            // Ensure safe teleportation with proper Y coordinate
            BlockPos safePos = findSafePosition(newLocation.level, newLocation.position);

            player.teleportTo(newLocation.level,
                            safePos.getX() + 0.5,
                            safePos.getY(),
                            safePos.getZ() + 0.5,
                            newLocation.yRot,
                            newLocation.xRot);

            // Play teleport sound
            newLocation.level.playSound(null, safePos, SoundEvents.ENDERMAN_TELEPORT, SoundSource.PLAYERS, 1.0f, 1.0f);
        }
    }

    // Helper class to store complete player location data
    private static class PlayerLocation {
        final BlockPos position;
        final ServerLevel level;
        final float yRot;
        final float xRot;

        PlayerLocation(BlockPos position, ServerLevel level, float yRot, float xRot) {
            this.position = position;
            this.level = level;
            this.yRot = yRot;
            this.xRot = xRot;
        }
    }

    // Find a safe position to teleport to (avoid suffocation/void)
    private BlockPos findSafePosition(ServerLevel level, BlockPos originalPos) {
        // First try the original position
        if (isSafePosition(level, originalPos)) {
            return originalPos;
        }

        // Try positions around the original location
        for (int dy = 0; dy <= 10; dy++) {
            BlockPos testPos = originalPos.offset(0, dy, 0);
            if (isSafePosition(level, testPos)) {
                return testPos;
            }

            if (dy > 0) {
                testPos = originalPos.offset(0, -dy, 0);
                if (isSafePosition(level, testPos)) {
                    return testPos;
                }
            }
        }

        // Fallback to world spawn if no safe position found
        return level.getSharedSpawnPos();
    }

    // Check if a position is safe for teleportation
    private boolean isSafePosition(ServerLevel level, BlockPos pos) {
        // Check bounds
        if (!level.isInWorldBounds(pos) || pos.getY() < level.getMinBuildHeight() || pos.getY() > level.getMaxBuildHeight()) {
            return false;
        }

        // Check if there's space for the player (2 blocks high)
        BlockState blockAtFeet = level.getBlockState(pos);
        BlockState blockAtHead = level.getBlockState(pos.above());

        // Player needs air or passable blocks at feet and head level
        return !blockAtFeet.canOcclude() && !blockAtHead.canOcclude();
    }
}
