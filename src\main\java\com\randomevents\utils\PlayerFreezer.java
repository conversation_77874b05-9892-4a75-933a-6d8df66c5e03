package com.randomevents.utils;

import net.minecraft.server.MinecraftServer;
import net.minecraft.server.level.ServerPlayer;
import net.minecraft.world.phys.Vec3;
import net.minecraftforge.common.MinecraftForge;
import net.minecraftforge.event.TickEvent;
import net.minecraftforge.eventbus.api.SubscribeEvent;

import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

public class PlayerFreezer {
    private static final ScheduledExecutorService scheduler = Executors.newSingleThreadScheduledExecutor();
    // OPTIMIZED: Use concurrent collections for thread safety
    private static final Set<ServerPlayer> frozenPlayers = ConcurrentHashMap.newKeySet();
    private static final Map<ServerPlayer, Vec3> frozenPositions = new ConcurrentHashMap<>();
    private static volatile boolean freezerActive = false;
    
    /**
     * Freeze all players on the server for a specified duration
     * @param server The minecraft server
     * @param durationMs Duration in milliseconds to freeze players
     */
    public static void freezeAllPlayers(MinecraftServer server, long durationMs) {
        if (!freezerActive) {
            // Register event handler if not already active
            MinecraftForge.EVENT_BUS.register(PlayerFreezer.class);
            freezerActive = true;
        }
        
        // Freeze all current players
        for (ServerPlayer player : server.getPlayerList().getPlayers()) {
            freezePlayer(player);
        }
        
        // Schedule unfreeze after duration
        scheduler.schedule(() -> {
            unfreezeAllPlayers(server);
        }, durationMs, TimeUnit.MILLISECONDS);
    }
    
    /**
     * Freeze a specific player
     * @param player The player to freeze
     */
    public static void freezePlayer(ServerPlayer player) {
        frozenPlayers.add(player);
        frozenPositions.put(player, player.position());
    }
    
    /**
     * Unfreeze a specific player
     * @param player The player to unfreeze
     */
    public static void unfreezePlayer(ServerPlayer player) {
        frozenPlayers.remove(player);
        frozenPositions.remove(player);
    }
    
    /**
     * Unfreeze all players and cleanup
     * @param server The minecraft server
     */
    public static void unfreezeAllPlayers(MinecraftServer server) {
        frozenPlayers.clear();
        frozenPositions.clear();
        
        if (freezerActive) {
            try {
                MinecraftForge.EVENT_BUS.unregister(PlayerFreezer.class);
            } catch (Exception e) {
                // Ignore if already unregistered
            }
            freezerActive = false;
        }
    }
    
    /**
     * Check if a player is currently frozen
     * @param player The player to check
     * @return true if the player is frozen
     */
    public static boolean isPlayerFrozen(ServerPlayer player) {
        return frozenPlayers.contains(player);
    }
    
    @SubscribeEvent
    public static void onPlayerTick(TickEvent.PlayerTickEvent event) {
        if (!freezerActive || event.phase != TickEvent.Phase.START) return;
        
        if (event.player instanceof ServerPlayer serverPlayer) {
            if (frozenPlayers.contains(serverPlayer)) {
                Vec3 frozenPos = frozenPositions.get(serverPlayer);
                if (frozenPos != null) {
                    Vec3 currentPos = serverPlayer.position();
                    
                    // Check if player has moved from frozen position
                    double distance = frozenPos.distanceTo(currentPos);
                    if (distance > 0.1) { // Allow tiny movements for stability
                        // Teleport back to frozen position
                        serverPlayer.teleportTo(frozenPos.x, frozenPos.y, frozenPos.z);
                    }
                    
                    // Cancel ALL movement including horizontal and vertical
                    Vec3 currentVelocity = serverPlayer.getDeltaMovement();
                    if (Math.abs(currentVelocity.x) > 0.001 || Math.abs(currentVelocity.z) > 0.001 || Math.abs(currentVelocity.y) > 0.001) {
                        serverPlayer.setDeltaMovement(0, 0, 0);
                        serverPlayer.hurtMarked = true; // Force update
                    }
                    
                    // Additional prevention: Reset impulse and movement flags
                    if (serverPlayer.hasImpulse) {
                        serverPlayer.hasImpulse = false;
                    }
                }
            }
        }
    }
}
