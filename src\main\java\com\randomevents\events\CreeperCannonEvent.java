package com.randomevents.events;

import net.minecraft.core.BlockPos;
import net.minecraft.server.MinecraftServer;
import net.minecraft.server.level.ServerLevel;
import net.minecraft.server.level.ServerPlayer;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.sounds.SoundSource;
import net.minecraft.world.entity.EntityType;
import net.minecraft.world.entity.monster.Creeper;
import net.minecraft.world.phys.Vec3;
import net.minecraftforge.common.MinecraftForge;
import net.minecraftforge.event.level.ExplosionEvent;
import net.minecraftforge.eventbus.api.SubscribeEvent;

import java.util.ArrayList;
import java.util.List;
import java.util.Random;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

public class CreeperCannonEvent extends RandomEvent {
    private static final ScheduledExecutorService scheduler = Executors.newSingleThreadScheduledExecutor();
    private static final Random random = new Random();
    private static final List<Creeper> launchedCreepers = new ArrayList<>();
    private static boolean eventActive = false;
    
    @Override
    public String getId() {
        return "creeper_cannon";
    }
    
    @Override
    public String getName() {
        return "§2§lCreeper Cannon!";
    }
    
    @Override
    public String getDescription() {
        return "5 homing missile creepers launched at each player, one every 2-4 seconds";
    }
    
    @Override
    public void execute(MinecraftServer server) {
        launchedCreepers.clear();
        eventActive = true;

        // Register explosion prevention handler
        MinecraftForge.EVENT_BUS.register(ExplosionPrevention.class);

        // Show warning message
        for (ServerPlayer player : server.getPlayerList().getPlayers()) {
            player.connection.send(new net.minecraft.network.protocol.game.ClientboundSetActionBarTextPacket(
                net.minecraft.network.chat.Component.literal("§2§lCreeper cannons detected... §a§lIncoming explosive projectiles!")));
        }
        
        // Wait 3 seconds then start launching creepers
        scheduler.schedule(() -> {
            // Launch exactly 5 creepers per player, one at a time every 2-4 seconds
            for (ServerPlayer player : server.getPlayerList().getPlayers()) {
                launchCreepersForPlayer(server, player, 5); // 5 creepers per player
            }

            // Clean up after 30 seconds (enough time for 5 creepers at 2-4 second intervals)
            scheduler.schedule(() -> {
                // Remove any remaining launched creepers
                for (Creeper creeper : launchedCreepers) {
                    if (creeper.isAlive()) {
                        creeper.discard();
                    }
                }
                launchedCreepers.clear();
                eventActive = false;

                // Unregister explosion prevention handler
                try {
                    MinecraftForge.EVENT_BUS.unregister(ExplosionPrevention.class);
                } catch (Exception e) {
                    // Ignore if already unregistered
                }

                // Show end message
                for (ServerPlayer player : server.getPlayerList().getPlayers()) {
                    player.connection.send(new net.minecraft.network.protocol.game.ClientboundSetActionBarTextPacket(
                        net.minecraft.network.chat.Component.literal("§7§lCreeper cannons offline... §aThe bombardment has ended.")));
                }
            }, 30L, TimeUnit.SECONDS);

        }, 3L, TimeUnit.SECONDS); // 3 second delay after warning
    }

    private void launchCreepersForPlayer(MinecraftServer server, ServerPlayer player, int creeperCount) {
        // Launch creepers one at a time with 2-4 second delays
        for (int i = 0; i < creeperCount; i++) {
            int delay = 2000 + random.nextInt(2000); // 2-4 seconds
            scheduler.schedule(() -> {
                if (player.isAlive()) {
                    launchCreeperAtPlayer(server, player);

                    // Show cannon fire message for this specific launch
                    String[] cannonMessages = {
                        "§2§lCREEPER CANNON FIRE!",
                        "§a§lINCOMING MISSILE!",
                        "§2§lHOMING CREEPER LAUNCHED!",
                        "§a§lMISSILE INCOMING!",
                        "§2§lCREEPER ARTILLERY FIRE!"
                    };
                    String message = cannonMessages[random.nextInt(cannonMessages.length)];
                    player.connection.send(new net.minecraft.network.protocol.game.ClientboundSetActionBarTextPacket(
                        net.minecraft.network.chat.Component.literal(message)));
                }
            }, i * delay, TimeUnit.MILLISECONDS);
        }
    }

    private void launchCreeperAtPlayer(MinecraftServer server, ServerPlayer player) {
        ServerLevel level = player.serverLevel();
        Vec3 playerPos = player.position();
        
        // Choose a random launch position 80-120 blocks away from the player
        double angle = random.nextDouble() * 2 * Math.PI; // Random angle
        double distance = 80 + random.nextDouble() * 40; // 80-120 blocks away
        
        double launchX = playerPos.x + Math.cos(angle) * distance;
        double launchZ = playerPos.z + Math.sin(angle) * distance;
        double launchY = level.getHeight(net.minecraft.world.level.levelgen.Heightmap.Types.MOTION_BLOCKING, (int) launchX, (int) launchZ) + 20 + random.nextInt(20); // 20-40 blocks high
        
        BlockPos launchPos = new BlockPos((int) launchX, (int) launchY, (int) launchZ);
        
        // Thread-safe entity spawning
        server.execute(() -> {
            try {
                // Create the creeper
                Creeper creeper = EntityType.CREEPER.create(level);
                if (creeper != null) {
                    creeper.moveTo(launchPos.getX() + 0.5, launchPos.getY(), launchPos.getZ() + 0.5);

                    // Calculate initial trajectory to player (will be adjusted by homing system)
                    Vec3 launchDirection = playerPos.subtract(creeper.position()).normalize();

                    // Set high initial velocity towards the player
                    double speed = 2.0; // High speed missile
                    Vec3 velocity = launchDirection.scale(speed);

                    // Add upward velocity for dramatic arc
                    velocity = velocity.add(0, 0.8, 0); // Strong upward boost

                    creeper.setDeltaMovement(velocity);

                    // Make creeper target player
                    creeper.setTarget(player); // Target the player

            // Configure creeper to prevent normal explosions
            // Use reflection or NBT to disable normal explosion behavior

            // Mark this creeper as a "weak missile creeper" using NBT
            net.minecraft.nbt.CompoundTag nbt = new net.minecraft.nbt.CompoundTag();
            creeper.saveWithoutId(nbt);
            nbt.putBoolean("WeakMissileCreeper", true); // Custom tag to identify weak creepers
            nbt.putInt("ExplosionRadius", 0); // No normal explosion radius
            nbt.putBoolean("powered", false); // Not powered (charged)
            creeper.load(nbt);
            
            // Add to level and tracking list
            level.addFreshEntity(creeper);
            launchedCreepers.add(creeper);
            
            // Play cannon fire sound at launch position
            level.playSound(null, launchPos, SoundEvents.GENERIC_EXPLODE, SoundSource.HOSTILE, 1.0f, 0.8f + random.nextFloat() * 0.4f);
            
            // Play incoming sound near player
            level.playSound(null, player.blockPosition(), SoundEvents.FIREWORK_ROCKET_LAUNCH, SoundSource.HOSTILE, 0.5f, 1.5f + random.nextFloat() * 0.5f);
            
            // Monitor creeper for landing and explosion enhancement
            monitorLaunchedCreeper(creeper, player);
        }
    }
    
    private void monitorLaunchedCreeper(Creeper creeper, ServerPlayer targetPlayer) {
        // Check every 0.1 seconds for up to 20 seconds - make it a homing missile
        for (int i = 0; i < 200; i++) { // 20 seconds / 0.1 = 200 checks
            scheduler.schedule(() -> {
                if (!creeper.isAlive() || !targetPlayer.isAlive()) {
                    launchedCreepers.remove(creeper);
                    return;
                }

                ServerLevel level = (ServerLevel) creeper.level();
                Vec3 creeperPos = creeper.position();
                Vec3 playerPos = targetPlayer.position();

                // Calculate direction to player (homing missile behavior)
                Vec3 directionToPlayer = playerPos.subtract(creeperPos).normalize();
                double distanceToPlayer = creeperPos.distanceTo(playerPos);

                // HOMING MISSILE BEHAVIOR - constantly adjust trajectory toward player
                if (distanceToPlayer > 3.0) { // If not too close, keep homing
                    double speed = 1.8; // Fast missile speed

                    // Add some upward velocity if creeper is below player
                    double upwardBoost = 0;
                    if (creeperPos.y < playerPos.y - 2) {
                        upwardBoost = 0.4;
                    }

                    // Set velocity directly toward player
                    Vec3 homingVelocity = directionToPlayer.scale(speed).add(0, upwardBoost, 0);
                    creeper.setDeltaMovement(homingVelocity);

                    // Make creeper face the player
                    creeper.setTarget(targetPlayer);

                    // Play missile sound occasionally
                    if (random.nextInt(10) == 0) { // 10% chance
                        level.playSound(null, creeper.blockPosition(), SoundEvents.FIREWORK_ROCKET_BLAST, SoundSource.HOSTILE, 0.3f, 2.0f);
                    }
                } else {
                    // Close to player - create weak custom explosion!
                    createWeakExplosion(level, creeper, targetPlayer);

                    // Remove the creeper without normal explosion
                    creeper.discard();

                    launchedCreepers.remove(creeper);
                    return;
                }

                // Show homing messages occasionally
                if (random.nextInt(50) == 0) { // 2% chance
                    targetPlayer.connection.send(new net.minecraft.network.protocol.game.ClientboundSetActionBarTextPacket(
                        net.minecraft.network.chat.Component.literal("§c§lHOMING CREEPER MISSILE INCOMING! §4§lRUN!")));
                }

            }, i * 100L, TimeUnit.MILLISECONDS); // Every 0.1 seconds for smooth homing
        }

        // Failsafe: Remove creeper from tracking after 20 seconds
        scheduler.schedule(() -> {
            launchedCreepers.remove(creeper);
        }, 20L, TimeUnit.SECONDS);
    }

    private void createWeakExplosion(ServerLevel level, Creeper creeper, ServerPlayer targetPlayer) {
        BlockPos explosionPos = creeper.blockPosition();

        // Play explosion sound
        level.playSound(null, explosionPos, SoundEvents.GENERIC_EXPLODE, SoundSource.HOSTILE, 1.0f, 1.0f);

        // Create visual explosion effect (no block damage)
        level.explode(null, explosionPos.getX(), explosionPos.getY(), explosionPos.getZ(),
                     1.0f, false, net.minecraft.world.level.Level.ExplosionInteraction.NONE);

        // Deal exactly 1 heart (2 HP) of damage to nearby players
        for (ServerPlayer player : level.getServer().getPlayerList().getPlayers()) {
            if (player.serverLevel() == level) {
                double distance = player.distanceToSqr(creeper.position());
                if (distance < 25) { // Within 5 blocks
                    // Deal exactly 1 heart of damage
                    player.hurt(level.damageSources().explosion(null, creeper), 2.0f); // 2.0f = 1 heart

                    // Show impact message
                    player.connection.send(new net.minecraft.network.protocol.game.ClientboundSetActionBarTextPacket(
                        net.minecraft.network.chat.Component.literal("§c§lHOMING CREEPER IMPACT! §4§l-1 Heart!")));
                }
            }
        }

        // Play impact sound
        level.playSound(null, explosionPos, SoundEvents.ANVIL_LAND, SoundSource.HOSTILE, 0.8f, 1.2f);
    }

    // Event handler to prevent normal creeper explosions during the event
    public static class ExplosionPrevention {
        @SubscribeEvent
        public static void onExplosion(ExplosionEvent.Start event) {
            if (!eventActive) return;

            // Check if this explosion is from one of our missile creepers
            if (event.getExplosion().getExploder() instanceof Creeper creeper) {
                if (launchedCreepers.contains(creeper)) {
                    // Cancel the normal explosion - we'll handle it with our custom weak explosion
                    event.setCanceled(true);

                    // Find the target player for this creeper
                    ServerPlayer targetPlayer = null;
                    if (creeper.getTarget() instanceof ServerPlayer) {
                        targetPlayer = (ServerPlayer) creeper.getTarget();
                    }

                    // Create our custom weak explosion instead
                    if (targetPlayer != null && creeper.level() instanceof ServerLevel serverLevel) {
                        // Use a static reference to create the weak explosion
                        createStaticWeakExplosion(serverLevel, creeper, targetPlayer);

                        // Remove creeper from tracking
                        launchedCreepers.remove(creeper);

                        // Remove the creeper
                        creeper.discard();
                    }
                }
            }
        }
    }

    // Static method for explosion prevention handler to use
    private static void createStaticWeakExplosion(ServerLevel level, Creeper creeper, ServerPlayer targetPlayer) {
        BlockPos explosionPos = creeper.blockPosition();

        // Play explosion sound
        level.playSound(null, explosionPos, SoundEvents.GENERIC_EXPLODE, SoundSource.HOSTILE, 1.0f, 1.0f);

        // Create visual explosion effect (no block damage)
        level.explode(null, explosionPos.getX(), explosionPos.getY(), explosionPos.getZ(),
                     1.0f, false, net.minecraft.world.level.Level.ExplosionInteraction.NONE);

        // Deal exactly 1 heart (2 HP) of damage to nearby players
        for (ServerPlayer player : level.getServer().getPlayerList().getPlayers()) {
            if (player.serverLevel() == level) {
                double distance = player.distanceToSqr(creeper.position());
                if (distance < 25) { // Within 5 blocks
                    // Deal exactly 1 heart of damage
                    player.hurt(level.damageSources().explosion(null, creeper), 2.0f); // 2.0f = 1 heart

                    // Show impact message
                    player.connection.send(new net.minecraft.network.protocol.game.ClientboundSetActionBarTextPacket(
                        net.minecraft.network.chat.Component.literal("§c§lHOMING CREEPER IMPACT! §4§l-1 Heart!")));
                }
            }
        }

        // Play impact sound
        level.playSound(null, explosionPos, SoundEvents.ANVIL_LAND, SoundSource.HOSTILE, 0.8f, 1.2f);
    }
}
