package com.randomevents.events;

import net.minecraft.core.BlockPos;
import net.minecraft.server.MinecraftServer;
import net.minecraft.server.level.ServerLevel;
import net.minecraft.server.level.ServerPlayer;
import net.minecraft.world.level.block.Blocks;

import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ThreadLocalRandom;
import java.util.concurrent.TimeUnit;

public class DesertStormEvent extends RandomEvent {
    private static final ScheduledExecutorService scheduler = Executors.newSingleThreadScheduledExecutor();
    
    @Override
    public String getId() {
        return "desert_storm";
    }
    
    @Override
    public String getName() {
        return "§e§lDesert Storm!";
    }
    
    @Override
    public String getDescription() {
        return "MASSIVE sand storm - tons of sand falls from the sky everywhere";
    }
    
    @Override
    public void execute(MinecraftServer server) {
        // MASSIVE sand storm for 30 seconds - THREAD SAFE
        for (int i = 0; i < 30; i++) {
            scheduler.schedule(() -> {
                for (ServerPlayer player : server.getPlayerList().getPlayers()) {
                    ServerLevel level = player.serverLevel();
                    BlockPos playerPos = player.blockPosition();

                    // Thread-safe block placement - execute on main server thread
                    server.execute(() -> {
                        try {
                            // Drop INSANE amounts of sand blocks - 40-60 individual blocks per second per player
                            int sandCount = 40 + ThreadLocalRandom.current().nextInt(21); // 40-60 blocks
                            for (int j = 0; j < sandCount; j++) {
                                int x = playerPos.getX() + ThreadLocalRandom.current().nextInt(50) - 25; // Larger area
                                int z = playerPos.getZ() + ThreadLocalRandom.current().nextInt(50) - 25;
                                int y = level.getHeight(net.minecraft.world.level.levelgen.Heightmap.Types.MOTION_BLOCKING, x, z) + 20 + ThreadLocalRandom.current().nextInt(15);

                                BlockPos sandPos = new BlockPos(x, y, z);

                                // Only place if the position is air
                                if (level.getBlockState(sandPos).isAir()) {
                                    level.setBlock(sandPos, Blocks.SAND.defaultBlockState(), 3);
                                }
                            }

                            // Also drop 8-12 massive 4x4 sand chunks per player per second
                            int chunkCount = 8 + ThreadLocalRandom.current().nextInt(5); // 8-12 chunks
                            for (int k = 0; k < chunkCount; k++) {
                                int centerX = playerPos.getX() + ThreadLocalRandom.current().nextInt(60) - 30; // Even larger area for chunks
                                int centerZ = playerPos.getZ() + ThreadLocalRandom.current().nextInt(60) - 30;
                                int centerY = level.getHeight(net.minecraft.world.level.levelgen.Heightmap.Types.MOTION_BLOCKING, centerX, centerZ) + 25 + ThreadLocalRandom.current().nextInt(20);

                                // Create 4x4 sand chunk
                                for (int dx = -2; dx <= 1; dx++) {
                                    for (int dz = -2; dz <= 1; dz++) {
                                        for (int dy = 0; dy < 4; dy++) { // Make chunks 4 blocks tall too
                                            BlockPos chunkPos = new BlockPos(centerX + dx, centerY + dy, centerZ + dz);

                                            if (level.isInWorldBounds(chunkPos) && level.getBlockState(chunkPos).isAir()) {
                                                level.setBlock(chunkPos, Blocks.SAND.defaultBlockState(), 3);
                                            }
                                        }
                                    }
                                }
                            }

                            // Add TONS of red sand for variety and extra chaos
                            int redSandCount = 25 + ThreadLocalRandom.current().nextInt(16); // 25-40 red sand blocks
                            for (int j = 0; j < redSandCount; j++) {
                                int x = playerPos.getX() + ThreadLocalRandom.current().nextInt(40) - 20;
                                int z = playerPos.getZ() + ThreadLocalRandom.current().nextInt(40) - 20;
                                int y = level.getHeight(net.minecraft.world.level.levelgen.Heightmap.Types.MOTION_BLOCKING, x, z) + 18 + ThreadLocalRandom.current().nextInt(12);

                                BlockPos redSandPos = new BlockPos(x, y, z);

                                if (level.getBlockState(redSandPos).isAir()) {
                                    level.setBlock(redSandPos, Blocks.RED_SAND.defaultBlockState(), 3);
                                }
                            }
                        } catch (Exception e) {
                            // Prevent individual block placement failures from crashing server
                            System.err.println("Desert storm block placement error: " + e.getMessage());
                        }
                    });
                }
            }, i * 1L, TimeUnit.SECONDS);
        }
    }
}
