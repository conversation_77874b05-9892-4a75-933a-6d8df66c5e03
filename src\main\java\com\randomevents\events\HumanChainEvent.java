package com.randomevents.events;

import net.minecraft.core.BlockPos;
import net.minecraft.server.MinecraftServer;
import net.minecraft.server.level.ServerLevel;
import net.minecraft.server.level.ServerPlayer;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.sounds.SoundSource;
import net.minecraft.world.level.biome.Biome;
import net.minecraft.world.level.biome.Biomes;
import net.minecraft.core.Holder;
import net.minecraft.world.level.levelgen.Heightmap;

import java.util.List;
import java.util.Random;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

public class HumanChainEvent extends RandomEvent {
    private static final ScheduledExecutorService scheduler = Executors.newSingleThreadScheduledExecutor();
    private static final Random random = new Random();
    
    @Override
    public String getId() {
        return "human_chain";
    }
    
    @Override
    public String getName() {
        return "§e§lHuman Chain!";
    }
    
    @Override
    public String getDescription() {
        return "Players must stay within 5 blocks of each other or take damage for 30 seconds";
    }
    
    @Override
    public void execute(MinecraftServer server) {
        // Show warning message
        for (ServerPlayer player : server.getPlayerList().getPlayers()) {
            player.connection.send(new net.minecraft.network.protocol.game.ClientboundSetActionBarTextPacket(
                net.minecraft.network.chat.Component.literal("§e§lYou might want to stay close to each other...")));
        }
        
        // After 5 seconds, teleport all players together to a village biome and start the event
        scheduler.schedule(() -> {
            List<ServerPlayer> players = server.getPlayerList().getPlayers();
            if (players.isEmpty()) return;

            // Check if ALL players are in the nether
            boolean allPlayersInNether = true;
            ServerPlayer netherPlayer = null;

            for (ServerPlayer player : players) {
                if (player.serverLevel().dimension() == net.minecraft.world.level.Level.NETHER) {
                    if (netherPlayer == null) {
                        netherPlayer = player; // Remember first nether player
                    }
                } else {
                    allPlayersInNether = false;
                    break;
                }
            }

            BlockPos villageMeetingPoint;
            ServerLevel targetLevel;

            if (allPlayersInNether && netherPlayer != null) {
                // If everyone is in nether, teleport to one player's position in nether
                villageMeetingPoint = netherPlayer.blockPosition();
                targetLevel = netherPlayer.serverLevel();

                // Show different message for nether gathering
                for (ServerPlayer player : players) {
                    player.connection.send(new net.minecraft.network.protocol.game.ClientboundSetActionBarTextPacket(
                        net.minecraft.network.chat.Component.literal("§c§lGathering in the Nether for Human Chain! §7Stay together!")));
                }
            } else {
                // Normal behavior - find village in overworld
                ServerLevel overworld = server.overworld();
                targetLevel = overworld;

                // Find a safe starting position in overworld
                BlockPos searchCenter = overworld.getSharedSpawnPos();

                // If any players are in overworld, use their position as search center
                for (ServerPlayer player : players) {
                    if (player.serverLevel().dimension() == net.minecraft.world.level.Level.OVERWORLD) {
                        searchCenter = player.blockPosition();
                        break;
                    }
                }

                villageMeetingPoint = findNearestVillage(overworld, searchCenter);

                // Show village message
                for (ServerPlayer player : players) {
                    player.connection.send(new net.minecraft.network.protocol.game.ClientboundSetActionBarTextPacket(
                        net.minecraft.network.chat.Component.literal("§e§lTeleporting to village for Human Chain! §7Stay together!")));
                }
            }

            // Teleport all players to the meeting point
            for (int i = 0; i < players.size(); i++) {
                ServerPlayer player = players.get(i);
                if (player.isAlive() && player.getServer() != null) {
                    // Spread players out slightly so they don't overlap
                    double offsetX = (i % 3 - 1) * 1.5; // -1.5, 0, 1.5
                    double offsetZ = (i / 3 - 1) * 1.5;

                    player.teleportTo(targetLevel,
                        villageMeetingPoint.getX() + offsetX + 0.5,
                        villageMeetingPoint.getY(),
                        villageMeetingPoint.getZ() + offsetZ + 0.5,
                        player.getYRot(), player.getXRot());

                    // Play teleport sound
                    targetLevel.playSound(null, player.blockPosition(), SoundEvents.ENDERMAN_TELEPORT, SoundSource.PLAYERS, 1.0f, 1.0f);
                }
            }
            
            // Start the human chain damage check for 30 seconds (60 checks, every 0.5 seconds)
            for (int i = 0; i < 60; i++) {
                scheduler.schedule(() -> {
                    checkPlayerDistances(server);
                }, i * 500L, TimeUnit.MILLISECONDS);
            }
            
        }, 5L, TimeUnit.SECONDS);
    }

    private BlockPos findNearestVillage(ServerLevel level, BlockPos startPos) {
        try {
            // Use the structure locator to find the nearest village (like /locate structure command)
            net.minecraft.core.HolderSet<net.minecraft.world.level.levelgen.structure.Structure> villageStructures =
                level.registryAccess().registryOrThrow(net.minecraft.core.registries.Registries.STRUCTURE)
                    .getTag(net.minecraft.tags.StructureTags.VILLAGE).orElse(null);

            if (villageStructures != null) {
                // Search for village within 6400 blocks (100 chunks)
                com.mojang.datafixers.util.Pair<BlockPos, net.minecraft.core.Holder<net.minecraft.world.level.levelgen.structure.Structure>> result =
                    level.getChunkSource().getGenerator().findNearestMapStructure(
                        level, villageStructures, startPos, 100, false);

                if (result != null) {
                    BlockPos villagePos = result.getFirst();
                    // Find a safe location near the village
                    BlockPos safePos = findSafeLocation(level, villagePos, 50);
                    System.out.println("HUMAN CHAIN: Found village at " + villagePos + ", using safe position " + safePos);
                    return safePos;
                }
            }
        } catch (Exception e) {
            System.err.println("HUMAN CHAIN: Error finding village: " + e.getMessage());
        }

        // Fallback: try to find village biomes manually
        System.out.println("HUMAN CHAIN: No village found via structure locator, trying biome search...");
        return findVillageBiomeManual(level, startPos);
    }

    private BlockPos findVillageBiomeManual(ServerLevel level, BlockPos startPos) {
        // Search for village biomes within 2000 blocks
        int searchRadius = 2000;
        int attempts = 50; // Try 50 random locations

        for (int i = 0; i < attempts; i++) {
            // Generate random position within search radius
            int x = startPos.getX() + random.nextInt(searchRadius * 2) - searchRadius;
            int z = startPos.getZ() + random.nextInt(searchRadius * 2) - searchRadius;
            BlockPos checkPos = new BlockPos(x, 64, z); // Use Y=64 as reference

            // Get biome at this position
            Holder<Biome> biomeHolder = level.getBiome(checkPos);

            // Check if it's a village biome (Plains, Desert, Savanna, Taiga, Snowy Plains)
            if (biomeHolder.is(Biomes.PLAINS) ||
                biomeHolder.is(Biomes.DESERT) ||
                biomeHolder.is(Biomes.SAVANNA) ||
                biomeHolder.is(Biomes.TAIGA) ||
                biomeHolder.is(Biomes.SNOWY_PLAINS)) {

                // Get proper ground level
                int groundY = level.getHeight(Heightmap.Types.MOTION_BLOCKING_NO_LEAVES, x, z);
                BlockPos safePos = new BlockPos(x, groundY, z);

                // Verify it's a safe location (solid ground, air above)
                if (level.isInWorldBounds(safePos) &&
                    !level.getBlockState(safePos.below()).isAir() &&
                    level.getBlockState(safePos).isAir() &&
                    level.getBlockState(safePos.above()).isAir()) {

                    System.out.println("HUMAN CHAIN: Found village biome at " + checkPos + ", using safe position " + safePos);
                    return safePos;
                }
            }
        }

        // If no village biome found, return a safe location near start position
        System.out.println("HUMAN CHAIN: No village found, using safe location near start position");
        return findSafeLocation(level, startPos, 100);
    }

    private BlockPos findSafeLocation(ServerLevel level, BlockPos center, int radius) {
        for (int attempts = 0; attempts < 20; attempts++) {
            int x = center.getX() + random.nextInt(radius * 2) - radius;
            int z = center.getZ() + random.nextInt(radius * 2) - radius;
            int y = level.getHeight(Heightmap.Types.MOTION_BLOCKING_NO_LEAVES, x, z);

            BlockPos pos = new BlockPos(x, y, z);

            // Check if it's a safe location (solid ground, air above)
            if (level.isInWorldBounds(pos) &&
                !level.getBlockState(pos.below()).isAir() &&
                level.getBlockState(pos).isAir() &&
                level.getBlockState(pos.above()).isAir()) {
                return pos;
            }
        }

        // If no safe location found, return original position
        return center;
    }

    private void checkPlayerDistances(MinecraftServer server) {
        List<ServerPlayer> players = server.getPlayerList().getPlayers();
        if (players.size() < 2) return;
        
        for (ServerPlayer player : players) {
            if (!player.isAlive()) continue;
            
            boolean hasNearbyPlayer = false;
            
            // Check if this player has at least one other player within 5 blocks
            for (ServerPlayer otherPlayer : players) {
                if (otherPlayer == player || !otherPlayer.isAlive()) continue;
                
                // Check if they're in the same dimension
                if (player.serverLevel() != otherPlayer.serverLevel()) continue;
                
                double distance = player.distanceTo(otherPlayer);
                if (distance <= 5.0) {
                    hasNearbyPlayer = true;
                    break;
                }
            }
            
            // If no nearby player, deal half a heart of damage
            if (!hasNearbyPlayer) {
                player.hurt(player.serverLevel().damageSources().magic(), 1.0f); // 1.0f = half a heart
                
                // Show warning message
                player.connection.send(new net.minecraft.network.protocol.game.ClientboundSetActionBarTextPacket(
                    net.minecraft.network.chat.Component.literal("§c§lSTAY CLOSE TO YOUR TEAM! §4§l-0.5 Hearts!")));
                
                // Play hurt sound
                player.serverLevel().playSound(null, player.blockPosition(), SoundEvents.PLAYER_HURT, SoundSource.PLAYERS, 0.5f, 1.0f);
            }
        }
    }
}
