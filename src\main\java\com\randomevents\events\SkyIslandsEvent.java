package com.randomevents.events;

import net.minecraft.core.BlockPos;
import net.minecraft.server.MinecraftServer;
import net.minecraft.server.level.ServerLevel;
import net.minecraft.server.level.ServerPlayer;
import net.minecraft.world.entity.item.ItemEntity;
import net.minecraft.world.item.ItemStack;
import net.minecraft.world.item.Items;
import net.minecraft.world.level.block.Blocks;

import java.util.Random;

public class SkyIslandsEvent extends RandomEvent {
    private static final Random random = new Random();
    
    @Override
    public String getId() {
        return "sky_islands";
    }
    
    @Override
    public String getName() {
        return "§9§lSky Islands!";
    }
    
    @Override
    public String getDescription() {
        return "Floating dirt platforms appear above players";
    }
    
    @Override
    public void execute(MinecraftServer server) {
        for (ServerPlayer player : server.getPlayerList().getPlayers()) {
            ServerLevel level = player.serverLevel();
            BlockPos playerPos = player.blockPosition();
            
            // Create 3-5 floating islands
            int islandCount = 3 + random.nextInt(3);
            for (int i = 0; i < islandCount; i++) {
                int x = playerPos.getX() + random.nextInt(60) - 30;
                int z = playerPos.getZ() + random.nextInt(60) - 30;
                int y = playerPos.getY() + 20 + random.nextInt(30);
                
                BlockPos centerPos = new BlockPos(x, y, z);
                
                // Create a small floating island (7x7 platform with some variation)
                for (int dx = -3; dx <= 3; dx++) {
                    for (int dz = -3; dz <= 3; dz++) {
                        // Create a circular-ish shape
                        if (dx * dx + dz * dz <= 9) {
                            BlockPos islandPos = centerPos.offset(dx, 0, dz);
                            
                            if (level.isInWorldBounds(islandPos) && level.getBlockState(islandPos).isAir()) {
                                level.setBlock(islandPos, Blocks.DIRT.defaultBlockState(), 3);
                                
                                // Add some grass on top
                                BlockPos grassPos = islandPos.above();
                                if (level.getBlockState(grassPos).isAir()) {
                                    level.setBlock(grassPos, Blocks.GRASS_BLOCK.defaultBlockState(), 3);
                                }
                                
                                // Occasionally add a flower
                                if (random.nextInt(10) == 0) {
                                    BlockPos flowerPos = grassPos.above();
                                    if (level.getBlockState(flowerPos).isAir()) {
                                        level.setBlock(flowerPos, Blocks.DANDELION.defaultBlockState(), 3);
                                    }
                                }
                            }
                        }
                    }
                }

                // Add loot to the center of each island
                spawnIslandLoot(level, centerPos.above(2)); // 2 blocks above the center
            }
        }
    }

    private void spawnIslandLoot(ServerLevel level, BlockPos lootPos) {
        // Create valuable loot items
        ItemStack[] lootItems = {
            new ItemStack(Items.DIAMOND, 2 + random.nextInt(3)), // 2-4 diamonds
            new ItemStack(Items.EMERALD, 1 + random.nextInt(3)), // 1-3 emeralds
            new ItemStack(Items.GOLD_INGOT, 3 + random.nextInt(5)), // 3-7 gold ingots
            new ItemStack(Items.IRON_INGOT, 5 + random.nextInt(8)), // 5-12 iron ingots
            new ItemStack(Items.ENCHANTED_BOOK, 1), // 1 enchanted book
            new ItemStack(Items.ENDER_PEARL, 1 + random.nextInt(2)), // 1-2 ender pearls
            new ItemStack(Items.EXPERIENCE_BOTTLE, 3 + random.nextInt(5)) // 3-7 XP bottles
        };

        // Randomly select 2-4 different loot types
        int lootCount = 2 + random.nextInt(3);
        for (int i = 0; i < lootCount; i++) {
            ItemStack loot = lootItems[random.nextInt(lootItems.length)];

            // Create item entity
            ItemEntity itemEntity = new ItemEntity(level,
                lootPos.getX() + 0.5 + (random.nextDouble() - 0.5) * 2, // Spread items around
                lootPos.getY(),
                lootPos.getZ() + 0.5 + (random.nextDouble() - 0.5) * 2,
                loot.copy());

            // Give items a small upward velocity
            itemEntity.setDeltaMovement(
                (random.nextDouble() - 0.5) * 0.2, // Random X
                0.3, // Upward Y
                (random.nextDouble() - 0.5) * 0.2  // Random Z
            );

            level.addFreshEntity(itemEntity);
        }
    }
}
