package com.randomevents.events;

import net.minecraft.core.BlockPos;
import net.minecraft.server.MinecraftServer;
import net.minecraft.server.level.ServerLevel;
import net.minecraft.server.level.ServerPlayer;

import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ThreadLocalRandom;
import java.util.concurrent.TimeUnit;

public class DemolitionDayEvent extends RandomEvent {
    private static final ScheduledExecutorService scheduler = Executors.newSingleThreadScheduledExecutor();

    
    @Override
    public String getId() {
        return "demolition_day";
    }
    
    @Override
    public String getName() {
        return "§c§lDemolition Day!";
    }
    
    @Override
    public String getDescription() {
        return "Random explosions occur around players for 15 seconds";
    }
    
    @Override
    public void execute(MinecraftServer server) {
        // Show warning message
        for (ServerPlayer player : server.getPlayerList().getPlayers()) {
            player.connection.send(new net.minecraft.network.protocol.game.ClientboundSetActionBarTextPacket(
                net.minecraft.network.chat.Component.literal("§c§lDEMOLITION DAY! §7Random explosions incoming!")));
        }

        // Create random explosions for 15 seconds
        for (int i = 0; i < 15; i++) {
            scheduler.schedule(() -> {
                for (ServerPlayer player : server.getPlayerList().getPlayers()) {
                    ServerLevel level = player.serverLevel();
                    BlockPos playerPos = player.blockPosition();

                    // Create 2-4 explosions per second around each player
                    int explosionCount = 2 + ThreadLocalRandom.current().nextInt(3);
                    for (int j = 0; j < explosionCount; j++) {
                        // Random position 15-40 blocks away from player (audible but not too close)
                        double angle = ThreadLocalRandom.current().nextDouble() * 2 * Math.PI;
                        double distance = 15 + ThreadLocalRandom.current().nextDouble() * 25; // 15-40 blocks

                        int x = (int) (playerPos.getX() + Math.cos(angle) * distance);
                        int z = (int) (playerPos.getZ() + Math.sin(angle) * distance);
                        int y = level.getHeight(net.minecraft.world.level.levelgen.Heightmap.Types.MOTION_BLOCKING, x, z);

                        // Create explosion effect (no block damage, no entity damage)
                        level.explode(null, x, y, z, 3.0f, false,
                                    net.minecraft.world.level.Level.ExplosionInteraction.NONE);
                    }
                }
            }, i * 1L, TimeUnit.SECONDS);
        }

        // Show end message after 15 seconds
        scheduler.schedule(() -> {
            for (ServerPlayer player : server.getPlayerList().getPlayers()) {
                player.connection.send(new net.minecraft.network.protocol.game.ClientboundSetActionBarTextPacket(
                    net.minecraft.network.chat.Component.literal("§7§lThe explosions have stopped... §aFor now.")));
            }
        }, 15L, TimeUnit.SECONDS);
    }

}
