package com.randomevents.events;

import net.minecraft.core.BlockPos;
import net.minecraft.server.MinecraftServer;
import net.minecraft.server.level.ServerLevel;
import net.minecraft.server.level.ServerPlayer;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.sounds.SoundSource;

import net.minecraft.world.level.block.Blocks;
import net.minecraft.world.level.block.state.BlockState;

import java.util.*;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

public class NetherInvasionEvent extends RandomEvent {
    private static final ScheduledExecutorService scheduler = Executors.newSingleThreadScheduledExecutor();
    private static final Random random = new Random();
    private static final Map<ServerPlayer, List<BlockPos>> playerPortals = new HashMap<>();
    private static final List<BlockPos> allPortals = new ArrayList<>();
    private static final Set<ServerPlayer> playersWhoEnteredPortal = new HashSet<>();
    private static final Set<ServerPlayer> playersPortalsBroken = new HashSet<>(); // Track whose portals are broken
    private static boolean eventActive = false;

    @Override
    public String getId() {
        return "nether_invasion";
    }

    @Override
    public String getName() {
        return "§4§lNether Invasion!";
    }

    @Override
    public String getDescription() {
        return "Nether portals spawn around players - enter within 15 seconds or be eliminated!";
    }

    @Override
    public boolean canTrigger(MinecraftServer server) {
        // Only allow nether invasion to trigger at event #61 or later
        com.randomevents.manager.RandomEventManager manager = com.randomevents.RandomEventsMod.getEventManager();
        if (manager != null) {
            int currentEventNumber = manager.getCurrentEventNumber();
            // Only available from event 61 onwards
            return currentEventNumber >= 60; // 60 because next event will be 61
        }
        return false; // Default to false if manager not available
    }

    @Override
    public void execute(MinecraftServer server) {
        eventActive = true;
        playerPortals.clear();
        allPortals.clear();
        playersWhoEnteredPortal.clear();
        playersPortalsBroken.clear();
        
        // Show warning message
        for (ServerPlayer player : server.getPlayerList().getPlayers()) {
            player.connection.send(new net.minecraft.network.protocol.game.ClientboundSetActionBarTextPacket(
                net.minecraft.network.chat.Component.literal("§4§lThe Nether is breaking through...")));
        }

        // Wait 2 seconds then spawn portals
        scheduler.schedule(() -> {
            ServerLevel overworld = server.overworld();
            
            // Spawn MANY nether portals around each player (5-8 portals per player)
            for (ServerPlayer player : server.getPlayerList().getPlayers()) {
                int portalCount = 5 + random.nextInt(4); // 5-8 portals per player
                List<BlockPos> playerPortalList = new ArrayList<>();

                for (int i = 0; i < portalCount; i++) {
                    BlockPos portalPos = createNetherPortalNearPlayer(overworld, player);
                    if (portalPos != null) {
                        // Store portal for this player
                        playerPortalList.add(portalPos);
                        // Also store in global list for easy access
                        allPortals.add(portalPos);
                    }
                }

                // Store the list of portals for this player
                if (!playerPortalList.isEmpty()) {
                    playerPortals.put(player, playerPortalList);
                }
            }

            // Show urgent message for 5 seconds before countdown
            for (ServerPlayer player : server.getPlayerList().getPlayers()) {
                player.connection.send(new net.minecraft.network.protocol.game.ClientboundSetActionBarTextPacket(
                    net.minecraft.network.chat.Component.literal("§4§lENTER ANY PORTAL OR BE ELIMINATED IN 15 SECONDS!")));
            }

            // Play dramatic sound
            overworld.playSound(null, BlockPos.ZERO, SoundEvents.WITHER_SPAWN, SoundSource.HOSTILE, 2.0f, 0.5f);

            // Wait 5 seconds, then start countdown and elimination timer
            scheduler.schedule(() -> {
                startCountdownAndElimination(server);
            }, 5L, TimeUnit.SECONDS);

        }, 2L, TimeUnit.SECONDS);
    }

    private BlockPos createNetherPortalNearPlayer(ServerLevel level, ServerPlayer player) {
        BlockPos playerPos = player.blockPosition();
        
        // Find a suitable location 5-25 blocks away (wider spread for more portals)
        for (int attempts = 0; attempts < 30; attempts++) {
            int distance = 5 + random.nextInt(21); // 5-25 blocks away
            double angle = random.nextDouble() * 2 * Math.PI;
            int x = playerPos.getX() + (int)(Math.cos(angle) * distance);
            int z = playerPos.getZ() + (int)(Math.sin(angle) * distance);
            int y = level.getHeight(net.minecraft.world.level.levelgen.Heightmap.Types.MOTION_BLOCKING, x, z);
            
            BlockPos portalBase = new BlockPos(x, y, z);
            
            // Check if area is clear
            boolean canPlace = true;
            for (int dx = -1; dx <= 1; dx++) {
                for (int dy = 0; dy <= 4; dy++) {
                    for (int dz = -1; dz <= 1; dz++) {
                        BlockPos checkPos = portalBase.offset(dx, dy, dz);
                        if (!level.getBlockState(checkPos).isAir() && dy > 0) {
                            canPlace = false;
                            break;
                        }
                    }
                    if (!canPlace) break;
                }
                if (!canPlace) break;
            }
            
            if (canPlace) {
                // Create the nether portal frame
                createPortalFrame(level, portalBase);
                
                // Light the portal
                level.setBlock(portalBase.offset(0, 1, 0), Blocks.NETHER_PORTAL.defaultBlockState(), 3);
                level.setBlock(portalBase.offset(0, 2, 0), Blocks.NETHER_PORTAL.defaultBlockState(), 3);
                
                // Play portal sound
                level.playSound(null, portalBase, SoundEvents.PORTAL_AMBIENT, SoundSource.BLOCKS, 1.0f, 1.0f);
                
                return portalBase;
            }
        }
        return null;
    }

    private void createPortalFrame(ServerLevel level, BlockPos base) {
        // Create obsidian frame for nether portal
        BlockState obsidian = Blocks.OBSIDIAN.defaultBlockState();
        
        // Bottom frame
        level.setBlock(base.offset(-1, 0, 0), obsidian, 3);
        level.setBlock(base.offset(0, 0, 0), obsidian, 3);
        level.setBlock(base.offset(1, 0, 0), obsidian, 3);
        
        // Side frames
        level.setBlock(base.offset(-1, 1, 0), obsidian, 3);
        level.setBlock(base.offset(-1, 2, 0), obsidian, 3);
        level.setBlock(base.offset(-1, 3, 0), obsidian, 3);
        level.setBlock(base.offset(1, 1, 0), obsidian, 3);
        level.setBlock(base.offset(1, 2, 0), obsidian, 3);
        level.setBlock(base.offset(1, 3, 0), obsidian, 3);
        
        // Top frame
        level.setBlock(base.offset(-1, 4, 0), obsidian, 3);
        level.setBlock(base.offset(0, 4, 0), obsidian, 3);
        level.setBlock(base.offset(1, 4, 0), obsidian, 3);
    }

    private void startCountdownAndElimination(MinecraftServer server) {
        // Check for actual portal travel every 0.25 seconds during the remaining 10 second timer (more frequent)
        for (int i = 0; i < 40; i++) { // 10 seconds * 4 checks per second = 40 checks
            final int checkNumber = i; // Make variable final for lambda
            scheduler.schedule(() -> {
                if (!eventActive) return;

                // Check if any players have actually traveled to the nether
                for (ServerPlayer player : server.getPlayerList().getPlayers()) {
                    if (!playersWhoEnteredPortal.contains(player)) {
                        // Player is safe ONLY if they are currently in the nether dimension
                        if (player.serverLevel().dimension() == net.minecraft.world.level.Level.NETHER) {
                            playersWhoEnteredPortal.add(player);
                            System.out.println("NETHER INVASION: " + player.getName().getString() + " safely entered the nether at check #" + checkNumber);

                            // Break their NETHER-SIDE portal after 3.5 seconds
                            scheduler.schedule(() -> {
                                if (!playersPortalsBroken.contains(player)) {
                                    playersPortalsBroken.add(player);
                                    breakNetherPortalNearPlayer(server, player);

                                    // Show dramatic message
                                    player.connection.send(new net.minecraft.network.protocol.game.ClientboundSetActionBarTextPacket(
                                        net.minecraft.network.chat.Component.literal("§4§lYour way back is gone forever...")));
                                }
                            }, 3500L, TimeUnit.MILLISECONDS); // 3.5 seconds
                        }
                    }
                }
            }, i * 250L, TimeUnit.MILLISECONDS); // Every 0.25 seconds (more frequent)
        }

        // Countdown from 10 to 1 (last 10 seconds of the remaining timer)
        for (int i = 10; i >= 1; i--) {
            final int count = i;
            scheduler.schedule(() -> {
                if (!eventActive) return;

                for (ServerPlayer player : server.getPlayerList().getPlayers()) {
                    if (!playersWhoEnteredPortal.contains(player)) {
                        player.connection.send(new net.minecraft.network.protocol.game.ClientboundSetActionBarTextPacket(
                            net.minecraft.network.chat.Component.literal("§c§l" + count + " seconds remaining!")));
                    }
                }

                // Play countdown sound
                server.overworld().playSound(null, BlockPos.ZERO, SoundEvents.ANVIL_LAND, SoundSource.BLOCKS, 1.0f, 2.0f);

            }, (10 - i) * 1000L, TimeUnit.MILLISECONDS); // 10 seconds countdown
        }

        // Elimination after 10 seconds (total 15 seconds from initial message)
        scheduler.schedule(() -> {
            if (!eventActive) return;

            // FINAL COMPREHENSIVE CHECK - catch any players who might have been missed
            System.out.println("NETHER INVASION: Performing final check before elimination...");
            for (ServerPlayer player : server.getPlayerList().getPlayers()) {
                if (!playersWhoEnteredPortal.contains(player)) {
                    // Last chance check - if player is in nether, mark them as safe
                    if (player.serverLevel().dimension() == net.minecraft.world.level.Level.NETHER) {
                        playersWhoEnteredPortal.add(player);
                        System.out.println("NETHER INVASION: FINAL CHECK - " + player.getName().getString() + " found safe in nether!");

                        // Still break their portal after 3.5 seconds
                        scheduler.schedule(() -> {
                            if (!playersPortalsBroken.contains(player)) {
                                playersPortalsBroken.add(player);
                                breakNetherPortalNearPlayer(server, player);

                                // Show dramatic message
                                player.connection.send(new net.minecraft.network.protocol.game.ClientboundSetActionBarTextPacket(
                                    net.minecraft.network.chat.Component.literal("§4§lYour way back is gone forever...")));
                            }
                        }, 3500L, TimeUnit.MILLISECONDS);
                    }
                }
            }

            // Now determine who to eliminate
            List<ServerPlayer> playersToEliminate = new ArrayList<>();
            for (ServerPlayer player : server.getPlayerList().getPlayers()) {
                if (!playersWhoEnteredPortal.contains(player)) {
                    playersToEliminate.add(player);
                    System.out.println("NETHER INVASION: " + player.getName().getString() + " will be eliminated (dimension: " + player.serverLevel().dimension() + ")");
                } else {
                    System.out.println("NETHER INVASION: " + player.getName().getString() + " is safe (dimension: " + player.serverLevel().dimension() + ")");
                }
            }

            // Eliminate players who didn't enter portals
            for (ServerPlayer player : playersToEliminate) {
                // Show elimination message (smaller text using action bar)
                for (ServerPlayer allPlayer : server.getPlayerList().getPlayers()) {
                    allPlayer.connection.send(new net.minecraft.network.protocol.game.ClientboundSetActionBarTextPacket(
                        net.minecraft.network.chat.Component.literal("§4§l" + player.getName().getString() + " was consumed by the Nether!")));
                }

                // Kill the player
                player.hurt(server.overworld().damageSources().magic(), 1000.0f);
                
                // Create dramatic explosion
                BlockPos playerPos = player.blockPosition();
                server.overworld().explode(null, playerPos.getX(), playerPos.getY(), playerPos.getZ(), 
                    3.0f, false, net.minecraft.world.level.Level.ExplosionInteraction.NONE);
            }

            // Clean up after 2 minutes
            scheduler.schedule(() -> {
                cleanupEvent(server);
            }, 120L, TimeUnit.SECONDS);

        }, 10L, TimeUnit.SECONDS); // 10 seconds after countdown starts (15 total)
    }

    private void breakNetherPortalNearPlayer(MinecraftServer server, ServerPlayer player) {
        // Break the nether-side portal near the player's current position
        server.execute(() -> {
            try {
                // Make sure player is still in the nether
                if (player.serverLevel().dimension() != net.minecraft.world.level.Level.NETHER) {
                    System.out.println("NETHER INVASION: Player " + player.getName().getString() + " is no longer in nether, skipping portal break");
                    return;
                }

                ServerLevel nether = player.serverLevel(); // Get the nether level
                BlockPos playerPos = player.blockPosition();

                // Search for nether portal blocks in a 10x10x10 area around the player
                boolean foundPortal = false;
                for (int dx = -5; dx <= 5; dx++) {
                    for (int dy = -5; dy <= 5; dy++) {
                        for (int dz = -5; dz <= 5; dz++) {
                            BlockPos checkPos = playerPos.offset(dx, dy, dz);
                            if (nether.getBlockState(checkPos).is(Blocks.NETHER_PORTAL)) {
                                // Found a portal block, break the entire portal structure
                                breakPortalStructure(nether, checkPos);
                                foundPortal = true;
                                break;
                            }
                        }
                        if (foundPortal) break;
                    }
                    if (foundPortal) break;
                }

                if (foundPortal) {
                    System.out.println("NETHER INVASION: Broke nether-side portal for " + player.getName().getString() + " at " + playerPos);
                } else {
                    System.out.println("NETHER INVASION: No nether portal found near " + player.getName().getString() + " at " + playerPos);
                }
            } catch (Exception e) {
                System.err.println("Nether portal breaking error: " + e.getMessage());
            }
        });
    }

    private void breakPortalStructure(ServerLevel level, BlockPos startPos) {
        // Break all connected portal blocks starting from the found position
        // Search in a 5x5x5 area to break the entire portal structure
        for (int dx = -2; dx <= 2; dx++) {
            for (int dy = -2; dy <= 2; dy++) {
                for (int dz = -2; dz <= 2; dz++) {
                    BlockPos checkPos = startPos.offset(dx, dy, dz);
                    BlockState state = level.getBlockState(checkPos);
                    if (state.is(Blocks.NETHER_PORTAL)) {
                        // Replace portal blocks with air
                        level.setBlock(checkPos, Blocks.AIR.defaultBlockState(), 3);
                    }
                }
            }
        }
    }

    private void cleanupEvent(MinecraftServer server) {
        eventActive = false;

        // Thread-safe portal cleanup - execute on main server thread
        server.execute(() -> {
            try {
                // Remove portals
                ServerLevel overworld = server.overworld();
                for (BlockPos portalPos : allPortals) {
                    // Remove portal blocks in 3x5 area
                    for (int dx = -1; dx <= 1; dx++) {
                        for (int dy = 0; dy <= 4; dy++) {
                            BlockPos removePos = portalPos.offset(dx, dy, 0);
                            BlockState state = overworld.getBlockState(removePos);
                            if (state.is(Blocks.NETHER_PORTAL) || state.is(Blocks.OBSIDIAN)) {
                                overworld.setBlock(removePos, Blocks.AIR.defaultBlockState(), 3);
                            }
                        }
                    }
                }
            } catch (Exception e) {
                // Prevent cleanup errors from crashing server
                System.err.println("Nether invasion cleanup error: " + e.getMessage());
            }
        });

        // Clear tracking data
        playerPortals.clear();
        allPortals.clear();
        playersWhoEnteredPortal.clear();
        playersPortalsBroken.clear();
        
        // Show cleanup message
        for (ServerPlayer player : server.getPlayerList().getPlayers()) {
            player.connection.send(new net.minecraft.network.protocol.game.ClientboundSetActionBarTextPacket(
                net.minecraft.network.chat.Component.literal("§7§lThe Nether invasion ends... §8The portals close.")));
        }
    }

    // Portal entry is now detected by checking if players are in the nether dimension
    // No manual portal entry method needed
}
