package com.randomevents.commands;

import net.minecraft.core.BlockPos;
import net.minecraft.server.MinecraftServer;
import net.minecraft.server.level.ServerLevel;
import net.minecraft.server.level.ServerPlayer;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.sounds.SoundSource;
import net.minecraft.world.effect.MobEffectInstance;
import net.minecraft.world.effect.MobEffects;

import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import net.minecraft.world.phys.Vec3;
import net.minecraftforge.common.MinecraftForge;
import net.minecraftforge.event.TickEvent;
import net.minecraftforge.eventbus.api.SubscribeEvent;
import com.randomevents.utils.PlayerFreezer;

public class EliminationCeremony {
    private static final ScheduledExecutorService scheduler = Executors.newSingleThreadScheduledExecutor();
    private static final Set<ServerPlayer> frozenPlayers = new HashSet<>();
    private static final Map<ServerPlayer, BlockPos> originalPositions = new HashMap<>();
    private static boolean ceremonyActive = false;
    
    public static void execute(ServerPlayer targetPlayer) {
        MinecraftServer server = targetPlayer.getServer();
        if (server == null) return;

        ceremonyActive = true;
        ServerLevel level = targetPlayer.serverLevel();
        List<ServerPlayer> allPlayers = server.getPlayerList().getPlayers();
        List<ServerPlayer> spectators = new ArrayList<>();

        // Save original positions for ALL players
        originalPositions.clear();
        for (ServerPlayer player : allPlayers) {
            originalPositions.put(player, player.blockPosition());
        }

        // Remove target player from spectators list
        for (ServerPlayer player : allPlayers) {
            if (!player.equals(targetPlayer)) {
                spectators.add(player);
            }
        }

        // Freeze ONLY the target player - spectators can move freely
        freezePlayer(targetPlayer);

        // Register movement prevention event handler
        MinecraftForge.EVENT_BUS.register(MovementPrevention.class);
        
        // Find execution location high in the sky
        BlockPos executionSite = findExecutionSite(level, targetPlayer.blockPosition());

        // Create the sky platform
        createSkyPlatform(level, executionSite);

        if (!spectators.isEmpty()) {
            // Phase 1: Teleport all spectators in a line
            teleportSpectatorsInLine(spectators, executionSite, level);
        }

        // Phase 2: Teleport target player to execution site (even if alone)
        teleportTargetToExecutionSite(targetPlayer, executionSite, level);

        // Phase 3: Wait 5 seconds, then start the elimination ceremony
        scheduler.schedule(() -> {
            startEliminationSequence(targetPlayer, spectators);
        }, 5L, TimeUnit.SECONDS);
    }
    
    private static BlockPos findExecutionSite(ServerLevel level, BlockPos startPos) {
        // Create execution site high in the sky (200 blocks up)
        int skyY = Math.max(200, level.getMaxBuildHeight() - 50);
        return new BlockPos(startPos.getX(), skyY, startPos.getZ());
    }

    private static void createSkyPlatform(ServerLevel level, BlockPos center) {
        // Create a 15x15 platform in the sky
        for (int x = -7; x <= 7; x++) {
            for (int z = -7; z <= 7; z++) {
                BlockPos platformPos = center.offset(x, -1, z); // One block below center
                level.setBlock(platformPos, net.minecraft.world.level.block.Blocks.OBSIDIAN.defaultBlockState(), 3);
            }
        }

        // Removed extra blackstone platform as requested

        // Add some dramatic pillars around the platform
        for (int i = 0; i < 4; i++) {
            int x = (i % 2 == 0) ? -6 : 6;
            int z = (i < 2) ? -6 : 6;

            for (int y = 0; y <= 5; y++) {
                BlockPos pillarPos = center.offset(x, y, z);
                level.setBlock(pillarPos, net.minecraft.world.level.block.Blocks.OBSIDIAN.defaultBlockState(), 3);
            }
        }
    }
    
    private static void teleportSpectatorsInLine(List<ServerPlayer> spectators, BlockPos executionSite, ServerLevel level) {
        // Teleport spectators around the execution site in a circle, NOT on the center platform
        for (int i = 0; i < spectators.size(); i++) {
            ServerPlayer spectator = spectators.get(i);

            // Position spectators in a circle around the execution site, OUTSIDE the center platform
            double angle = (2 * Math.PI * i) / spectators.size(); // Distribute evenly in a circle
            double radius = 10; // 10 blocks away from center (outside the 15x15 platform center)

            double x = executionSite.getX() + Math.cos(angle) * radius;
            double z = executionSite.getZ() + Math.sin(angle) * radius;
            double y = executionSite.getY(); // Same level as execution site

            // Calculate yaw to face the execution site (center)
            float yaw = (float) Math.toDegrees(Math.atan2(executionSite.getZ() - z, executionSite.getX() - x));

            spectator.teleportTo(level, x + 0.5, y, z + 0.5, yaw, 0);

            // Create small individual platforms for each spectator (3x3 obsidian)
            for (int dx = -1; dx <= 1; dx++) {
                for (int dz = -1; dz <= 1; dz++) {
                    BlockPos platformPos = new BlockPos((int)x + dx, (int)y - 1, (int)z + dz);
                    level.setBlock(platformPos, net.minecraft.world.level.block.Blocks.OBSIDIAN.defaultBlockState(), 3);
                }
            }

            // Play teleport sound
            level.playSound(null, spectator.blockPosition(), SoundEvents.ENDERMAN_TELEPORT, SoundSource.PLAYERS, 1.0f, 1.0f);
        }

        // Show message to spectators
        for (ServerPlayer spectator : spectators) {
            spectator.connection.send(new net.minecraft.network.protocol.game.ClientboundSetActionBarTextPacket(
                net.minecraft.network.chat.Component.literal("§7§lYou have been gathered to witness the elimination...")));
        }

        // Freeze all spectators for 2 seconds after teleportation (target is already frozen)
        if (!spectators.isEmpty()) {
            PlayerFreezer.freezeAllPlayers(level.getServer(), 2000L);
        }
    }
    
    private static void teleportTargetToExecutionSite(ServerPlayer targetPlayer, BlockPos executionSite, ServerLevel level) {
        // Create elevated execution platform 3 blocks higher than the main platform
        BlockPos elevatedSite = executionSite.offset(0, 3, 0);

        // Create 3 obsidian blocks as a small platform for the target
        level.setBlock(elevatedSite.offset(0, -1, 0), net.minecraft.world.level.block.Blocks.OBSIDIAN.defaultBlockState(), 3); // Center
        level.setBlock(elevatedSite.offset(-1, -1, 0), net.minecraft.world.level.block.Blocks.OBSIDIAN.defaultBlockState(), 3); // Left
        level.setBlock(elevatedSite.offset(1, -1, 0), net.minecraft.world.level.block.Blocks.OBSIDIAN.defaultBlockState(), 3); // Right

        // Teleport target player to the elevated execution site
        targetPlayer.teleportTo(level, elevatedSite.getX() + 0.5, elevatedSite.getY(), elevatedSite.getZ() + 0.5, 0, 0);

        // Play dramatic teleport sound
        level.playSound(null, elevatedSite, SoundEvents.ENDERMAN_TELEPORT, SoundSource.PLAYERS, 2.0f, 0.5f);

        // Show message to target
        targetPlayer.connection.send(new net.minecraft.network.protocol.game.ClientboundSetActionBarTextPacket(
            net.minecraft.network.chat.Component.literal("§c§lYou have been brought for elimination...")));
    }
    
    private static void startEliminationSequence(ServerPlayer targetPlayer, List<ServerPlayer> spectators) {
        ServerLevel level = targetPlayer.serverLevel();
        String playerName = targetPlayer.getName().getString();

        // Phase 1: First ceremonial text - "We gather here today..." (4 seconds)
        for (ServerPlayer player : targetPlayer.getServer().getPlayerList().getPlayers()) {
            // Clear any existing titles first
            player.connection.send(new net.minecraft.network.protocol.game.ClientboundSetTitleTextPacket(
                net.minecraft.network.chat.Component.literal("")));
            // Use subtitle for smaller text
            player.connection.send(new net.minecraft.network.protocol.game.ClientboundSetSubtitleTextPacket(
                net.minecraft.network.chat.Component.literal("§7We gather here today...")));
            player.connection.send(new net.minecraft.network.protocol.game.ClientboundSetTitlesAnimationPacket(
                5, 75, 5)); // Fade in 0.25s, stay 3.75s, fade out 0.25s (total 4 seconds)
        }

        // Play solemn sound
        level.playSound(null, targetPlayer.blockPosition(), SoundEvents.WITHER_SPAWN, SoundSource.HOSTILE, 0.8f, 0.6f);

        // Phase 2: Second ceremonial text - "For the execution of playername..." (4 seconds after first text starts)
        scheduler.schedule(() -> {
            for (ServerPlayer player : targetPlayer.getServer().getPlayerList().getPlayers()) {
                // Clear previous subtitle first
                player.connection.send(new net.minecraft.network.protocol.game.ClientboundSetSubtitleTextPacket(
                    net.minecraft.network.chat.Component.literal("")));
                // Show second ceremonial text
                player.connection.send(new net.minecraft.network.protocol.game.ClientboundSetSubtitleTextPacket(
                    net.minecraft.network.chat.Component.literal("§cFor the execution of " + playerName + "...")));
                player.connection.send(new net.minecraft.network.protocol.game.ClientboundSetTitlesAnimationPacket(
                    5, 75, 5)); // Fade in 0.25s, stay 3.75s, fade out 0.25s (total 4 seconds)
            }

            // Play dramatic sound
            level.playSound(null, targetPlayer.blockPosition(), SoundEvents.ANVIL_LAND, SoundSource.HOSTILE, 1.0f, 0.8f);

        }, 4L, TimeUnit.SECONDS); // 4 seconds after first text starts (right when first text ends)

        // Phase 3: Start levitation (after 8 seconds total - right when second text ends)
        scheduler.schedule(() -> {
            // Clear any existing titles and subtitles
            for (ServerPlayer player : targetPlayer.getServer().getPlayerList().getPlayers()) {
                player.connection.send(new net.minecraft.network.protocol.game.ClientboundSetTitleTextPacket(
                    net.minecraft.network.chat.Component.literal("")));
                player.connection.send(new net.minecraft.network.protocol.game.ClientboundSetSubtitleTextPacket(
                    net.minecraft.network.chat.Component.literal("")));
            }

            // TEMPORARILY UNFREEZE the target player so levitation can work
            unfreezePlayer(targetPlayer);

            // Start levitating the target player
            targetPlayer.addEffect(new MobEffectInstance(MobEffects.LEVITATION, 80, 1, false, false)); // 4 seconds of levitation

            // Play rising sound
            level.playSound(null, targetPlayer.blockPosition(), SoundEvents.FIREWORK_ROCKET_LAUNCH, SoundSource.PLAYERS, 1.5f, 1.2f);

            // Show levitation message to spectators
            for (ServerPlayer spectator : spectators) {
                spectator.connection.send(new net.minecraft.network.protocol.game.ClientboundSetActionBarTextPacket(
                    net.minecraft.network.chat.Component.literal("§6§l" + playerName + " is being lifted for execution...")));
            }

            // Show message to target
            targetPlayer.connection.send(new net.minecraft.network.protocol.game.ClientboundSetActionBarTextPacket(
                net.minecraft.network.chat.Component.literal("§c§lYou are being lifted for execution...")));

            // Re-freeze the target player after levitation ends (4 seconds later)
            scheduler.schedule(() -> {
                freezePlayer(targetPlayer);
            }, 4L, TimeUnit.SECONDS);

        }, 8L, TimeUnit.SECONDS);

        // Phase 4: Explosion (after 11 seconds total - 3 seconds of levitation)
        scheduler.schedule(() -> {
            BlockPos explosionPos = targetPlayer.blockPosition();

            // Create massive visual explosion FIRST
            level.explode(null, explosionPos.getX(), explosionPos.getY(), explosionPos.getZ(),
                         5.0f, false, net.minecraft.world.level.Level.ExplosionInteraction.NONE);

            // Kill the target player and put them in spectator mode
            targetPlayer.hurt(level.damageSources().explosion(null, null), 1000.0f);

            // Put executed player in spectator mode after death
            scheduler.schedule(() -> {
                if (!targetPlayer.isAlive()) {
                    targetPlayer.setGameMode(net.minecraft.world.level.GameType.SPECTATOR);
                }
            }, 100L, TimeUnit.MILLISECONDS); // Small delay to ensure death is processed

            // Play dramatic explosion sound
            level.playSound(null, explosionPos, SoundEvents.GENERIC_EXPLODE, SoundSource.HOSTILE, 3.0f, 0.5f);

            // Show elimination message AFTER the explosion (1 second delay)
            scheduler.schedule(() -> {
                for (ServerPlayer player : targetPlayer.getServer().getPlayerList().getPlayers()) {
                    // Clear title and use subtitle for smaller text
                    player.connection.send(new net.minecraft.network.protocol.game.ClientboundSetTitleTextPacket(
                        net.minecraft.network.chat.Component.literal("")));
                    player.connection.send(new net.minecraft.network.protocol.game.ClientboundSetSubtitleTextPacket(
                        net.minecraft.network.chat.Component.literal("§c" + playerName + " has been eliminated")));
                    player.connection.send(new net.minecraft.network.protocol.game.ClientboundSetTitlesAnimationPacket(
                        0, 60, 20)); // No fade in, stay 3s, fade out 1s
                }
            }, 1L, TimeUnit.SECONDS);

            // Release spectators after 5 seconds
            scheduler.schedule(() -> {
                // Teleport all living players back to their EXACT original positions
                for (ServerPlayer player : targetPlayer.getServer().getPlayerList().getPlayers()) {
                    if (player.isAlive() && originalPositions.containsKey(player)) {
                        BlockPos originalPos = originalPositions.get(player);
                        ServerLevel playerLevel = player.serverLevel();

                        // Teleport back to the exact original position where they were first teleported from
                        player.teleportTo(playerLevel, originalPos.getX() + 0.5, originalPos.getY(), originalPos.getZ() + 0.5, player.getYRot(), player.getXRot());

                        // Unfreeze the player (though spectators were never frozen)
                        unfreezePlayer(player);

                        player.connection.send(new net.minecraft.network.protocol.game.ClientboundSetActionBarTextPacket(
                            net.minecraft.network.chat.Component.literal("§7§lThe execution is complete. You have been returned.")));
                    }
                }

                // Also handle the executed player if they're still around (in spectator mode)
                if (originalPositions.containsKey(targetPlayer)) {
                    BlockPos originalPos = originalPositions.get(targetPlayer);
                    ServerLevel playerLevel = targetPlayer.serverLevel();

                    // Teleport executed player back to their original position (they're in spectator mode)
                    targetPlayer.teleportTo(playerLevel, originalPos.getX() + 0.5, originalPos.getY(), originalPos.getZ() + 0.5, targetPlayer.getYRot(), targetPlayer.getXRot());

                    targetPlayer.connection.send(new net.minecraft.network.protocol.game.ClientboundSetActionBarTextPacket(
                        net.minecraft.network.chat.Component.literal("§c§lYou have been executed and are now in spectator mode.")));
                }

                // Clear all titles and subtitles
                for (ServerPlayer player : targetPlayer.getServer().getPlayerList().getPlayers()) {
                    player.connection.send(new net.minecraft.network.protocol.game.ClientboundClearTitlesPacket(true));
                }

                // Clean up the sky platform
                cleanupSkyPlatform(targetPlayer.serverLevel(), findExecutionSite(targetPlayer.serverLevel(), originalPositions.get(targetPlayer)));

                // End ceremony and unfreeze all players
                endCeremony();

            }, 5L, TimeUnit.SECONDS);

        }, 11L, TimeUnit.SECONDS);
    }

    private static void freezePlayer(ServerPlayer player) {
        frozenPlayers.add(player);
    }

    private static void unfreezePlayer(ServerPlayer player) {
        frozenPlayers.remove(player);
    }

    private static void cleanupSkyPlatform(ServerLevel level, BlockPos center) {
        // Remove the 15x15 obsidian platform (center execution platform)
        for (int x = -7; x <= 7; x++) {
            for (int z = -7; z <= 7; z++) {
                BlockPos platformPos = center.offset(x, -1, z); // One block below center
                level.setBlock(platformPos, net.minecraft.world.level.block.Blocks.AIR.defaultBlockState(), 3);
            }
        }

        // Remove any remaining blackstone execution platform (if it exists)
        for (int x = -2; x <= 2; x++) {
            for (int z = -2; z <= 2; z++) {
                BlockPos executionPos = center.offset(x, 0, z);
                level.setBlock(executionPos, net.minecraft.world.level.block.Blocks.AIR.defaultBlockState(), 3);
            }
        }

        // Remove the elevated execution platform (3 blocks higher)
        BlockPos elevatedCenter = center.offset(0, 3, 0);
        level.setBlock(elevatedCenter.offset(0, -1, 0), net.minecraft.world.level.block.Blocks.AIR.defaultBlockState(), 3); // Center
        level.setBlock(elevatedCenter.offset(-1, -1, 0), net.minecraft.world.level.block.Blocks.AIR.defaultBlockState(), 3); // Left
        level.setBlock(elevatedCenter.offset(1, -1, 0), net.minecraft.world.level.block.Blocks.AIR.defaultBlockState(), 3); // Right

        // Remove the dramatic pillars around the platform
        for (int i = 0; i < 4; i++) {
            int x = (i % 2 == 0) ? -6 : 6;
            int z = (i < 2) ? -6 : 6;

            for (int y = 0; y <= 5; y++) {
                BlockPos pillarPos = center.offset(x, y, z);
                level.setBlock(pillarPos, net.minecraft.world.level.block.Blocks.AIR.defaultBlockState(), 3);
            }
        }

        // Remove individual spectator platforms in a 25x25 area around the center
        for (int x = -12; x <= 12; x++) {
            for (int z = -12; z <= 12; z++) {
                // Skip the center platform area (already cleaned above)
                if (Math.abs(x) <= 7 && Math.abs(z) <= 7) {
                    continue;
                }

                BlockPos spectatorPlatformPos = center.offset(x, -1, z);
                if (level.getBlockState(spectatorPlatformPos).is(net.minecraft.world.level.block.Blocks.OBSIDIAN)) {
                    level.setBlock(spectatorPlatformPos, net.minecraft.world.level.block.Blocks.AIR.defaultBlockState(), 3);
                }
            }
        }
    }

    private static void endCeremony() {
        ceremonyActive = false;
        frozenPlayers.clear();
        originalPositions.clear();
        MovementPrevention.clearFrozenPositions();

        // Unregister movement prevention event handler
        try {
            MinecraftForge.EVENT_BUS.unregister(MovementPrevention.class);
        } catch (Exception e) {
            // Ignore if already unregistered
        }
    }

    // Event handler class for movement prevention
    public static class MovementPrevention {
        private static final Map<ServerPlayer, Vec3> frozenPositions = new HashMap<>();

        @SubscribeEvent
        public static void onPlayerTick(TickEvent.PlayerTickEvent event) {
            if (!ceremonyActive || event.phase != TickEvent.Phase.START) return;

            if (event.player instanceof ServerPlayer serverPlayer) {
                if (frozenPlayers.contains(serverPlayer)) {
                    // Store the frozen position when first frozen
                    if (!frozenPositions.containsKey(serverPlayer)) {
                        frozenPositions.put(serverPlayer, serverPlayer.position());
                    }

                    Vec3 frozenPos = frozenPositions.get(serverPlayer);
                    Vec3 currentPos = serverPlayer.position();

                    // Check if player has moved from frozen position
                    double distance = frozenPos.distanceTo(currentPos);
                    if (distance > 0.1) { // Allow tiny movements for stability
                        // Teleport back to frozen position
                        serverPlayer.teleportTo(frozenPos.x, frozenPos.y, frozenPos.z);
                    }

                    // AGGRESSIVE movement prevention - completely lock all movement
                    Vec3 currentVelocity = serverPlayer.getDeltaMovement();

                    // Cancel ALL movement including horizontal and vertical
                    if (Math.abs(currentVelocity.x) > 0.001 || Math.abs(currentVelocity.z) > 0.001 || Math.abs(currentVelocity.y) > 0.001) {
                        serverPlayer.setDeltaMovement(0, 0, 0);
                        serverPlayer.hurtMarked = true; // Force update
                    }

                    // Additional prevention: Reset impulse and movement flags
                    if (serverPlayer.hasImpulse) {
                        serverPlayer.hasImpulse = false;
                    }
                }
            }
        }

        public static void clearFrozenPositions() {
            frozenPositions.clear();
        }
    }
    

}
