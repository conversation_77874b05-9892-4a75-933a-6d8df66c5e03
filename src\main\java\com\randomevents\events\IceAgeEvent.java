package com.randomevents.events;

import net.minecraft.core.BlockPos;
import net.minecraft.server.MinecraftServer;
import net.minecraft.server.level.ServerLevel;
import net.minecraft.server.level.ServerPlayer;
import net.minecraft.world.level.block.Blocks;
import net.minecraft.world.level.block.state.BlockState;

import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ScheduledFuture;
import java.util.concurrent.TimeUnit;
import java.util.Map;
import java.util.HashMap;
import java.util.List;
import java.util.ArrayList;

public class IceAgeEvent extends RandomEvent {
    private static final ScheduledExecutorService scheduler = Executors.newSingleThreadScheduledExecutor();
    private final List<ScheduledFuture<?>> scheduledTasks = new ArrayList<>();
    private final Map<ServerPlayer, BlockPos> playerLastPositions = new HashMap<>();
    private static volatile boolean iceAgeActive = false;
    
    @Override
    public String getId() {
        return "ice_age";
    }
    
    @Override
    public String getName() {
        return "§b§lIce Age!";
    }
    
    @Override
    public String getDescription() {
        return "Ice slowly spreads across the ground toward players over 30 seconds";
    }
    
    @Override
    public void execute(MinecraftServer server) {
        scheduledTasks.clear();
        playerLastPositions.clear();
        iceAgeActive = true;

        // Show warning message
        for (ServerPlayer player : server.getPlayerList().getPlayers()) {
            player.connection.send(new net.minecraft.network.protocol.game.ClientboundSetActionBarTextPacket(
                net.minecraft.network.chat.Component.literal("§b§lIce Age! §7Ice spreads in the direction you run!")));

            // Store initial player position
            playerLastPositions.put(player, player.blockPosition());
        }

        // Start ice spreading - check every 1 second for 30 seconds (THREAD SAFE)
        ScheduledFuture<?> iceTask = scheduler.scheduleAtFixedRate(() -> {
            if (!iceAgeActive) return;

            try {
                for (ServerPlayer player : server.getPlayerList().getPlayers()) {
                    if (player.isAlive()) {
                        ServerLevel level = player.serverLevel();
                        BlockPos currentPos = player.blockPosition();
                        BlockPos lastPos = playerLastPositions.get(player);

                        if (lastPos != null) {
                            // Thread-safe ice spreading - execute on main server thread
                            server.execute(() -> {
                                try {
                                    spreadIceInDirection(level, lastPos, currentPos);
                                } catch (Exception e) {
                                    // Prevent individual ice spread failures from crashing server
                                    System.err.println("Ice spread error: " + e.getMessage());
                                }
                            });
                        }

                        // Update player's last position
                        playerLastPositions.put(player, currentPos);
                    }
                }
            } catch (Exception e) {
                // Ignore errors to prevent crashes
            }
        }, 1000L, 1000L, TimeUnit.MILLISECONDS); // Every 1 second (safer)
        scheduledTasks.add(iceTask);

        // Stop event after exactly 30 seconds
        ScheduledFuture<?> stopTask = scheduler.schedule(() -> {
            iceAgeActive = false;

            // Cancel all scheduled tasks
            for (ScheduledFuture<?> task : scheduledTasks) {
                if (!task.isDone()) {
                    task.cancel(false);
                }
            }
            scheduledTasks.clear();
            playerLastPositions.clear();

            // Show final message
            for (ServerPlayer player : server.getPlayerList().getPlayers()) {
                player.connection.send(new net.minecraft.network.protocol.game.ClientboundSetActionBarTextPacket(
                    net.minecraft.network.chat.Component.literal("§7§lIce age has ended! §b§lThe ice remains...")));
            }
        }, 30L, TimeUnit.SECONDS);
        scheduledTasks.add(stopTask);
    }

    private void spreadIceInDirection(ServerLevel level, BlockPos lastPos, BlockPos currentPos) {
        try {
            // Calculate direction player moved
            int deltaX = currentPos.getX() - lastPos.getX();
            int deltaZ = currentPos.getZ() - lastPos.getZ();

            // Spread ice in a smaller area to prevent crashes (reduced from 17x17 to 11x11)
            for (int x = -5; x <= 5; x++) {
                for (int z = -5; z <= 5; z++) {
                    // Focus more ice in the direction of movement
                    BlockPos icePos = new BlockPos(
                        currentPos.getX() + x + deltaX * 2, // Extend 2 blocks in movement direction (reduced)
                        currentPos.getY() - 1, // Floor level
                        currentPos.getZ() + z + deltaZ * 2  // Extend 2 blocks in movement direction (reduced)
                    );

                    if (level.isInWorldBounds(icePos)) {
                        BlockState blockState = level.getBlockState(icePos);

                        // Turn ground blocks into ice (reduced block types for performance)
                        if (blockState.is(Blocks.STONE) || blockState.is(Blocks.DIRT) ||
                            blockState.is(Blocks.GRASS_BLOCK) || blockState.is(Blocks.SAND) ||
                            blockState.is(Blocks.WATER)) {

                            level.setBlock(icePos, Blocks.ICE.defaultBlockState(), 2);
                        }
                    }
                }
            }

            // Also spread ice around the current position (smaller 11x11 area)
            for (int x = -5; x <= 5; x++) {
                for (int z = -5; z <= 5; z++) {
                    BlockPos currentIcePos = new BlockPos(currentPos.getX() + x, currentPos.getY() - 1, currentPos.getZ() + z);

                    if (level.isInWorldBounds(currentIcePos)) {
                        BlockState blockState = level.getBlockState(currentIcePos);

                        if (blockState.is(Blocks.STONE) || blockState.is(Blocks.DIRT) ||
                            blockState.is(Blocks.GRASS_BLOCK) || blockState.is(Blocks.SAND) ||
                            blockState.is(Blocks.WATER)) {

                            level.setBlock(currentIcePos, Blocks.ICE.defaultBlockState(), 2);
                        }
                    }
                }
            }
        } catch (Exception e) {
            // Ignore block update errors to prevent crashes
        }
    }
}
