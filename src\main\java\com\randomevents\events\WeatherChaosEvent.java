package com.randomevents.events;

import net.minecraft.server.MinecraftServer;
import net.minecraft.server.level.ServerLevel;

import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ThreadLocalRandom;
import java.util.concurrent.TimeUnit;

public class Weather<PERSON>haosEvent extends RandomEvent {
    private static final ScheduledExecutorService scheduler = Executors.newSingleThreadScheduledExecutor();
    
    @Override
    public String getId() {
        return "weather_chaos";
    }
    
    @Override
    public String getName() {
        return "§b§lWeather Chaos!";
    }
    
    @Override
    public String getDescription() {
        return "Weather changes rapidly and chaotically for 2 minutes";
    }

    @Override
    public void execute(MinecraftServer server) {
        // Change weather every 3 seconds for 2 minutes - THREAD SAFE
        for (int i = 0; i < 40; i++) { // 40 changes over 2 minutes
            scheduler.schedule(() -> {
                // Thread-safe weather modifications - execute on main server thread
                server.execute(() -> {
                    try {
                        for (ServerLevel level : server.getAllLevels()) {
                            if (level.dimension() == ServerLevel.OVERWORLD) {
                                int weatherType = ThreadLocalRandom.current().nextInt(5); // More weather variations
                                switch (weatherType) {
                                    case 0: // Clear
                                        level.setWeatherParameters(6000, 0, false, false);
                                        break;
                                    case 1: // Light Rain
                                        level.setWeatherParameters(0, 3000, true, false);
                                        break;
                                    case 2: // Heavy Rain
                                        level.setWeatherParameters(0, 6000, true, false);
                                        break;
                                    case 3: // Thunder
                                        level.setWeatherParameters(0, 6000, true, true);
                                        break;
                                    case 4: // Intense Thunder
                                        level.setWeatherParameters(0, 12000, true, true);
                                        break;
                                }

                                // Also randomly change time of day for extra chaos
                                if (ThreadLocalRandom.current().nextInt(3) == 0) { // 33% chance
                                    int timeType = ThreadLocalRandom.current().nextInt(4);
                                    switch (timeType) {
                                        case 0: level.setDayTime(1000); break;  // Morning
                                        case 1: level.setDayTime(6000); break;  // Noon
                                        case 2: level.setDayTime(13000); break; // Sunset
                                        case 3: level.setDayTime(18000); break; // Midnight
                                    }
                                }
                            }
                        }
                    } catch (Exception e) {
                        // Prevent individual weather change failures from crashing server
                        System.err.println("Weather chaos error: " + e.getMessage());
                    }
                });
            }, i * 3L, TimeUnit.SECONDS); // Every 3 seconds
        }
    }
}
